import SwiftUI

struct CreateExerciseView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: ExerciseViewModel
    @StateObject private var curriculumManager = CurriculumManager.shared
    @StateObject private var classroomViewModel = ClassroomViewModel()
    let selectedClassroomId: String // Pre-selected classroom (can be empty for "create from scratch")
    
    @State private var title = ""
    @State private var selectedTopic = ""
    @State private var selectedSubtopic = ""
    @State private var selectedClassroomIds: [String] = [] // Multiple classroom selection
    @State private var selectedDueDate = Calendar.current.date(bySettingHour: 23, minute: 59, second: 59, of: Date()) ?? Date() // Default to 23:59 of today
    @State private var questions: [Question] = []
    @State private var questionPresentationMode: QuestionPresentationMode?
    @State private var questionToDelete: Question?
    @State private var showingDeleteConfirmation = false
    @State private var topicSearchText = ""
    @State private var showingValidationAlert = false
    @State private var validationMessage = ""
    
    enum QuestionPresentationMode {
        case manualAdd
        case aiGeneration
        case editQuestion(Question)
    }
    
    // Determine grade based on selected classrooms
    private var currentGrade: Grade {
        // Get the grades of all selected classrooms
        let selectedClassrooms = classroomViewModel.classrooms.filter { selectedClassroomIds.contains($0.id) }
        let grades = Set(selectedClassrooms.map { $0.grade })

        // If all selected classrooms are from the same grade, use that grade
        if grades.count == 1, let grade = grades.first {
            return grade
        }

        // Default to form1 if no classrooms selected or mixed grades
        return .form1
    }

    // Check if selected classrooms have mixed grades
    private var hasMixedGrades: Bool {
        let selectedClassrooms = classroomViewModel.classrooms.filter { selectedClassroomIds.contains($0.id) }
        let grades = Set(selectedClassrooms.map { $0.grade })
        return grades.count > 1
    }
    
    // Available topics for the grade
    private var availableTopics: [String] {
        curriculumManager.getTopics(for: currentGrade)
    }
    
    // Available subtopics for the selected topic
    private var availableSubtopics: [String] {
        if selectedTopic.isEmpty {
            return []
        }
        return curriculumManager.getSubtopics(for: currentGrade, topic: selectedTopic)
    }
    
    // Filtered topics based on search
    private var filteredTopics: [String] {
        curriculumManager.searchTopics(for: currentGrade, searchText: topicSearchText)
    }

    // Sorted classrooms by form (grade) and then by name
    private var sortedClassrooms: [Classroom] {
        classroomViewModel.classrooms.sorted { lhs, rhs in
            // First sort by grade (form1, form2, form3)
            if lhs.grade != rhs.grade {
                // Define the order of grades
                let gradeOrder: [Grade] = [.form1, .form2, .form3]
                let lhsIndex = gradeOrder.firstIndex(of: lhs.grade) ?? 999
                let rhsIndex = gradeOrder.firstIndex(of: rhs.grade) ?? 999
                return lhsIndex < rhsIndex
            }
            // If same grade, sort by classroom name (1A, 1B, 1C, etc.)
            return lhs.name < rhs.name
        }
    }

    // Grouped classrooms by form for better organization
    private var groupedClassrooms: [(Grade, [Classroom])] {
        let gradeOrder: [Grade] = [.form1, .form2, .form3]
        return gradeOrder.compactMap { grade in
            let classroomsForGrade = classroomViewModel.classrooms
                .filter { $0.grade == grade }
                .sorted { $0.name < $1.name }
            return classroomsForGrade.isEmpty ? nil : (grade, classroomsForGrade)
        }
    }

    // Validation helper
    private var canCreateExercise: Bool {
        !title.isEmpty && !selectedTopic.isEmpty && !selectedClassroomIds.isEmpty && !questions.isEmpty
    }

    private func getValidationMessage() -> String {
        var missingFields: [String] = []

        if title.isEmpty {
            missingFields.append("Title")
        }
        if selectedClassroomIds.isEmpty {
            missingFields.append("Classroom assignment")
        }
        if selectedTopic.isEmpty {
            missingFields.append("Topic selection")
        }
        if questions.isEmpty {
            missingFields.append("At least one question")
        }

        if missingFields.count == 1 {
            return "Please provide: \(missingFields[0])"
        } else if missingFields.count == 2 {
            return "Please provide: \(missingFields[0]) and \(missingFields[1])"
        } else {
            let lastField = missingFields.removeLast()
            return "Please provide: \(missingFields.joined(separator: ", ")), and \(lastField)"
        }
    }
    
    var body: some View {
        NavigationStack {
            Form {
                Section("Exercise Details") {
                    TextField("Title", text: $title)
                    
                    // Classroom Selection with Grid Layout
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Assign to Classrooms")
                            .font(.headline)

                        if classroomViewModel.classrooms.isEmpty {
                            Text("No classrooms available. Create a classroom first.")
                                .font(.subheadline)
                                .foregroundColor(.orange)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(6)
                        } else {
                            // Helper text explaining the new behavior
                            Text("Select classrooms to automatically determine the grade and available topics")
                                .font(.caption)
                                .foregroundColor(.blue)
                                .padding(.bottom, 8)
                            ForEach(groupedClassrooms, id: \.0) { grade, classrooms in
                                VStack(alignment: .leading, spacing: 8) {
                                    // Form header
                                    Text(grade.displayName)
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.secondary)
                                        .padding(.top, 8)

                                    // Grid layout for classrooms - 4-5 columns in one row
                                    LazyVGrid(columns: [
                                        GridItem(.flexible(), spacing: 12),
                                        GridItem(.flexible(), spacing: 12),
                                        GridItem(.flexible(), spacing: 12),
                                        GridItem(.flexible(), spacing: 12),
                                        GridItem(.flexible(), spacing: 12)
                                    ], spacing: 12) {
                                        ForEach(classrooms) { classroom in
                                            ClassroomSelectionCard(
                                                classroom: classroom,
                                                isSelected: selectedClassroomIds.contains(classroom.id),
                                                onTap: {
                                                    if selectedClassroomIds.contains(classroom.id) {
                                                        selectedClassroomIds.removeAll { $0 == classroom.id }
                                                    } else {
                                                        selectedClassroomIds.append(classroom.id)
                                                    }

                                                    // Reset topic and subtopic when classroom selection changes
                                                    // This ensures topics are refreshed for the new grade
                                                    selectedTopic = ""
                                                    selectedSubtopic = ""
                                                    topicSearchText = ""
                                                }
                                            )
                                        }
                                    }
                                    .padding(.leading, 8)
                                }
                            }

                            if !selectedClassroomIds.isEmpty {
                                HStack {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.blue)
                                        .font(.caption)
                                    Text("\(selectedClassroomIds.count) classroom(s) selected")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                }
                                .padding(.top, 4)
                            }
                        }
                    }
                    
                    // Due Date Selection
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("Due Date")
                                .font(.headline)
                            Spacer()
                        }
                        DatePicker("", selection: $selectedDueDate, displayedComponents: [.date, .hourAndMinute])
                            .labelsHidden()
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }

                    // Show current grade based on selected classrooms
                    if !selectedClassroomIds.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("Grade:")
                                    .font(.headline)
                                Text(currentGrade.displayName)
                                    .font(.subheadline)
                                    .foregroundColor(.blue)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.blue.opacity(0.1))
                                    .cornerRadius(6)
                                Spacer()
                            }

                            // Show warning if mixed grades are selected
                            if hasMixedGrades {
                                HStack {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(.orange)
                                        .font(.caption)
                                    Text("Mixed grades selected. Topics will be shown for \(currentGrade.displayName) (default).")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                }
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.orange.opacity(0.1))
                                .cornerRadius(6)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                    // Topic Selection - Only show if classrooms are selected
                    if !selectedClassroomIds.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Topic")
                                .font(.headline)

                            if !selectedTopic.isEmpty {
                                HStack {
                                    TextField("Topic", text: $selectedTopic)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                    Button(action: {
                                        selectedTopic = ""
                                        selectedSubtopic = ""
                                        topicSearchText = ""
                                    }) {
                                        Image(systemName: "xmark.circle.fill")
                                            .foregroundColor(.gray)
                                    }
                                }
                            } else {
                                TextField("Search topics...", text: $topicSearchText)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())

                                TopicSelectionList(
                                    availableTopics: availableTopics,
                                    filteredTopics: filteredTopics,
                                    topicSearchText: topicSearchText,
                                    onTopicSelected: { topic in
                                        selectedTopic = topic
                                        selectedSubtopic = ""
                                        topicSearchText = topic
                                    }
                                )
                            }
                        }
                    } else {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Topic")
                                .font(.headline)
                                .foregroundColor(.gray)

                            Text("Please select classrooms first to see available topics")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .italic()
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(Color.gray.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                    
                    // Subtopic Selection
                    if !selectedTopic.isEmpty && !availableSubtopics.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            HStack {
                                Text("Subtopic")
                                    .font(.headline)
                                Spacer()
                            }
                            Picker("", selection: $selectedSubtopic) {
                                Text("Select Subtopic").tag("")
                                ForEach(availableSubtopics, id: \.self) { subtopic in
                                    Text(subtopic).tag(subtopic)
                                }
                            }
                            .labelsHidden()
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                    }
                    
                    // Add Question Buttons
                    VStack(spacing: 12) {
                        HStack(spacing: 12) {
                            Button(action: {
                                print("🔧 DEBUG: CreateExerciseView - AI Generation button tapped")
                                questionPresentationMode = .aiGeneration
                            }) {
                                HStack(spacing: 6) {
                                    Image(systemName: "wand.and.stars")
                                    Text("AI Generation")
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(Color.blue)
                                .foregroundColor(.white)
                                .cornerRadius(6)
                            }
                            .buttonStyle(.plain)
                            .disabled(selectedClassroomIds.isEmpty || selectedTopic.isEmpty)
                            
                            Button(action: {
                                print("🔧 DEBUG: CreateExerciseView - Manual Input button tapped")
                                questionPresentationMode = .manualAdd
                            }) {
                                HStack(spacing: 6) {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Manual Input")
                                }
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .background(Color.green)
                                .foregroundColor(.white)
                                .cornerRadius(6)
                            }
                            .buttonStyle(.plain)
                            
                            Spacer()
                        }
                        
                        if selectedClassroomIds.isEmpty {
                            Text("Please select classrooms first")
                                .font(.caption)
                                .foregroundColor(.gray)
                        } else if selectedTopic.isEmpty {
                            Text("Please select a topic to enable AI generation")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }

                }
                
                Section("Questions") {
                    ForEach(questions) { question in
                        QuestionRow(
                            question: question,
                            onEdit: {
                                print("🔧 DEBUG: Edit question button tapped")
                                questionPresentationMode = .editQuestion(question)
                            },
                            onDelete: {
                                questionToDelete = question
                                showingDeleteConfirmation = true
                            }
                        )
                    }
                    .onDelete(perform: deleteQuestions)
                }
            }
            .navigationTitle("Create Exercise")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                },
                trailing: Button("Create") {
                    if canCreateExercise {
                        createExercise()
                    } else {
                        validationMessage = getValidationMessage()
                        showingValidationAlert = true
                    }
                }
            )
            .fullScreenCover(item: Binding<PresentationItem?>(
                get: {
                    if let mode = questionPresentationMode {
                        return PresentationItem(mode: mode)
                    }
                    return nil
                },
                set: { _ in
                    questionPresentationMode = nil
                }
            )) { item in
                switch item.mode {
                case .manualAdd:
                    ManualAddQuestionView { question in
                        questions.append(question)
                    }
                case .aiGeneration:
                    AIQuestionGenerationView(
                        topic: selectedTopic,
                        subtopic: selectedSubtopic
                    ) { generatedQuestions in
                        questions.append(contentsOf: generatedQuestions)
                    }
                case .editQuestion(let question):
                    EditQuestionWrapperView(question: question) { updatedQuestion in
                        print("🔧 DEBUG: CreateExerciseView - onSave callback called with updated question")
                        if let index = questions.firstIndex(where: { $0.id == updatedQuestion.id }) {
                            print("🔧 DEBUG: CreateExerciseView - found question at index \(index), updating")
                            questions[index] = updatedQuestion
                        } else {
                            print("🔧 DEBUG: CreateExerciseView - ERROR: could not find question to update")
                        }
                    }
                }
            }
            .alert("Delete Question", isPresented: $showingDeleteConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Delete", role: .destructive) {
                    if let question = questionToDelete {
                        deleteQuestion(question)
                    }
                }
            } message: {
                Text("Are you sure you want to delete this question? This action cannot be undone.")
            }
            .alert("Cannot Create Exercise", isPresented: $showingValidationAlert) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(validationMessage)
            }
        }
        .task {
            // Load teacher's classrooms
            do {
                try await classroomViewModel.loadClassrooms()
                print("✅ CreateExerciseView: Loaded \(classroomViewModel.classrooms.count) classrooms")

                // Pre-select the suggested classroom if provided and valid
                if !selectedClassroomId.isEmpty && classroomViewModel.classrooms.contains(where: { $0.id == selectedClassroomId }) {
                    selectedClassroomIds = [selectedClassroomId]
                    print("✅ CreateExerciseView: Pre-selected classroom: \(selectedClassroomId)")
                }
            } catch {
                print("❌ CreateExerciseView: Failed to load classrooms: \(error.localizedDescription)")
            }
        }
    }

    private func createExercise() {
        print("📝 CreateExerciseView: Starting createExercise")
        print("📝 CreateExerciseView: Title: \(title)")
        print("📝 CreateExerciseView: Topic: \(selectedTopic)")
        print("📝 CreateExerciseView: Subtopic: \(selectedSubtopic)")
        print("📝 CreateExerciseView: Classroom IDs: \(selectedClassroomIds.joined(separator: ", "))")
        print("📝 CreateExerciseView: Due Date: \(selectedDueDate)")
        print("📝 CreateExerciseView: Number of Questions: \(questions.count)")
        print("📝 CreateExerciseView: Created By: \(UserManager.shared.currentUser.id)")

        // Log each question for debugging
        for (index, question) in questions.enumerated() {
            print("📝 CreateExerciseView: Question \(index + 1):")
            print("  - ID: \(question.id.uuidString)")
            print("  - Type: \(question.questionType.rawValue)")
            print("  - Text: \(question.questionText)")
            print("  - Points: \(question.points)")
            if let options = question.options {
                print("  - Options: \(options.joined(separator: ", "))")
            }
        }

        let exercise = Exercise(
            title: title,
            topic: selectedTopic,
            subtopic: selectedSubtopic,
            classroomIds: selectedClassroomIds,
            questions: questions,
            createdBy: UserManager.shared.currentUser.id,
            dueDate: selectedDueDate
        )

        print("📝 CreateExerciseView: Created exercise with ID: \(exercise.id.uuidString)")
        print("📝 CreateExerciseView: Calling viewModel.createExercise...")

        Task {
            do {
                try await viewModel.createExercise(exercise)
                print("📝 CreateExerciseView: createExercise completed successfully")
                await MainActor.run {
                    dismiss()
                    print("📝 CreateExerciseView: Dismissing view")
                }
            } catch {
                print("❌ CreateExerciseView: createExercise failed with error: \(error.localizedDescription)")
                await MainActor.run {
                    // You might want to show an error alert here
                    print("❌ CreateExerciseView: Error occurred during creation")
                }
            }
        }
    }

    private func deleteQuestion(_ question: Question) {
        questions.removeAll { $0.id == question.id }
        questionToDelete = nil
    }

    private func deleteQuestions(offsets: IndexSet) {
        questions.remove(atOffsets: offsets)
    }
}

// MARK: - Topic Selection Component
struct TopicSelectionList: View {
    let availableTopics: [String]
    let filteredTopics: [String]
    let topicSearchText: String
    let onTopicSelected: (String) -> Void

    var body: some View {
        let topicsToShow = topicSearchText.isEmpty ? availableTopics : filteredTopics

        if !topicsToShow.isEmpty {
            ScrollView {
                LazyVStack(alignment: .leading, spacing: 4) {
                    ForEach(topicsToShow, id: \.self) { topic in
                        Button(action: {
                            onTopicSelected(topic)
                        }) {
                            Text(topic)
                                .foregroundColor(.primary)
                                .padding(.vertical, 8)
                                .padding(.horizontal, 12)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .background(Color.clear)
                                .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            .frame(maxHeight: 200)
            .background(Color(.systemGray6))
            .cornerRadius(8)
        } else if !topicSearchText.isEmpty {
            Text("No topics found matching '\(topicSearchText)'")
                .font(.caption)
                .foregroundColor(.gray)
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .frame(maxWidth: .infinity, alignment: .leading)
                .background(Color(.systemGray6))
                .cornerRadius(8)
        }
    }
}

struct QuestionRow: View {
    let question: Question
    let onEdit: () -> Void
    let onDelete: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top, spacing: 12) {
                VStack(alignment: .leading, spacing: 4) {
                    // Question text with proper newline handling and truncation
                    QuestionTextRenderer(text: processQuestionText(question.questionText), fontSize: 18)
                        .lineLimit(4) // Limit to 4 lines to prevent overflow
                        .fixedSize(horizontal: false, vertical: true)

                    // Show truncation indicator if text is too long
                    if question.questionText.count > 200 {
                        Text("... (tap to edit for full text)")
                            .font(.caption2)
                            .foregroundColor(.gray)
                            .italic()
                    }

                    HStack(spacing: 8) {
                        Text(question.questionType.rawValue)
                            .font(.caption)
                            .foregroundColor(.gray)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(4)

                        Text("Points: \(question.points)")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(4)
                    }

                    // Only show options for multiple choice questions
                    if question.questionType == .multipleChoice, let options = question.options {
                        VStack(alignment: .leading, spacing: 2) {
                            ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                                HStack(alignment: .top, spacing: 8) {
                                    Text("\(Character(UnicodeScalar(65 + index)!)).")
                                        .font(.system(size: 18))
                                        .foregroundColor(.gray)
                                        .frame(width: 20, alignment: .leading)
                                    QuestionTextRenderer(text: processQuestionText(option), fontSize: 18)
                                        .foregroundColor(.gray)
                                        .lineLimit(2)
                                        .fixedSize(horizontal: false, vertical: true)
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                            }
                        }
                        .padding(.top, 4)
                    }
                }
                Spacer()
                VStack(spacing: 8) {
                    Button(action: onEdit) {
                        Image(systemName: "pencil")
                            .foregroundColor(.blue)
                            .font(.caption)
                    }
                    .buttonStyle(PlainButtonStyle())

                    Button(action: onDelete) {
                        Image(systemName: "trash")
                            .foregroundColor(.red)
                            .font(.caption)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(.vertical, 8)
    }
}

// Helper function to process question text
private func processQuestionText(_ text: String) -> String {
    return text.replacingOccurrences(of: "\\n", with: "\n")
}

// MARK: - Classroom Selection Card Component
struct ClassroomSelectionCard: View {
    let classroom: Classroom
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: 8) {
            // Classroom name - larger text size
            Text(classroom.name)
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
                .lineLimit(1)

            // Student count only (removed grade/form text)
            HStack(spacing: 3) {
                Image(systemName: "person.2.fill")
                    .font(.system(size: 10))
                    .foregroundColor(.gray)
                Text("\(classroom.students.count)")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                )
        )
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Helper Structs for Presentation
struct PresentationItem: Identifiable {
    let id = UUID()
    let mode: CreateExerciseView.QuestionPresentationMode
}

#Preview {
    CreateExerciseView(
        viewModel: ExerciseViewModel(),
        selectedClassroomId: ""
    )
}
