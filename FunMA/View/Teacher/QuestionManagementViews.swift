import SwiftUI

// MARK: - AI Question Generator
class AIQuestionGenerator {
    static let shared = AIQuestionGenerator()

    private init() {}

    func generateQuestions(
        topic: String,
        subtopic: String?,
        numberOfQuestions: Int,
        difficulty: String,
        questionTypes: [QuestionType],
        customPrompt: String? = nil
    ) async throws -> [Question] {
        print("🤖 AI Generation: Starting question generation")
        print("🤖 AI Generation: Topic: \(topic)")
        print("🤖 AI Generation: Subtopic: \(subtopic ?? "None")")
        print("🤖 AI Generation: Number of questions: \(numberOfQuestions)")
        print("🤖 AI Generation: Difficulty: \(difficulty)")

        // Prepare request payload
        var payload: [String: Any] = [
            "topic": topic,
            "difficulty": difficulty,
            "num_questions": numberOfQuestions,
            "deduct_credits": false // Set to false for testing
        ]

        // Add optional fields
        if let subtopic = subtopic, !subtopic.isEmpty {
            payload["subtopic"] = subtopic
        }

        if let customPrompt = customPrompt, !customPrompt.isEmpty {
            payload["custom_prompt"] = customPrompt
        }

        print("🤖 AI Generation: Request payload: \(payload)")

        // Make API request
        guard let url = URL(string: APIConfig.llmGenerateMCQuestionsEndpoint) else {
            throw NSError(domain: "AIQuestionGenerator", code: 400, userInfo: [
                NSLocalizedDescriptionKey: "Invalid API URL"
            ])
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authentication headers
        if let authHeaders = UserManager.shared.getAuthorizationHeader() {
            for (key, value) in authHeaders {
                request.addValue(value, forHTTPHeaderField: key)
            }
        }

        // Encode request body
        do {
            let jsonData = try JSONSerialization.data(withJSONObject: payload)
            request.httpBody = jsonData
        } catch {
            throw NSError(domain: "AIQuestionGenerator", code: 400, userInfo: [
                NSLocalizedDescriptionKey: "Failed to encode request: \(error.localizedDescription)"
            ])
        }

        // Send request
        let (data, response) = try await URLSession.shared.data(for: request)

        // Check HTTP response
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Invalid response"
            ])
        }

        print("🤖 AI Generation: HTTP Status: \(httpResponse.statusCode)")

        if httpResponse.statusCode != 200 {
            let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
            print("❌ AI Generation: Error response: \(errorMessage)")
            throw NSError(domain: "AIQuestionGenerator", code: httpResponse.statusCode, userInfo: [
                NSLocalizedDescriptionKey: "API Error (\(httpResponse.statusCode)): \(errorMessage)"
            ])
        }

        // Parse response
        return try parseQuestionsResponse(data: data)
    }

    private func parseQuestionsResponse(data: Data) throws -> [Question] {
        print("🔍 AI Generation: Parsing response data")

        // Convert data to string for debugging
        if let responseString = String(data: data, encoding: .utf8) {
            print("🔍 AI Generation: Raw response: \(responseString.prefix(500))...")
        }

        // Parse JSON response
        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Invalid JSON response"
            ])
        }

        print("🔍 AI Generation: Outer response structure: \(json.keys)")

        // Extract questions from the nested response structure
        // Based on the long questions API pattern, the response might be nested
        var questionsData: [[String: Any]] = []

        if let generatedContent = json["generated_content"] as? [String: Any],
           let textResponse = generatedContent["textResponse"] as? String {
            // Extract JSON from markdown code blocks if present
            let cleanedResponse = extractJSONFromMarkdown(textResponse)
            print("🔍 AI Generation: Cleaned response: \(cleanedResponse.prefix(200))...")

            // Try to parse the cleaned textResponse as JSON
            if let textData = cleanedResponse.data(using: .utf8) {
                do {
                    let parsedJSON = try JSONSerialization.jsonObject(with: textData) as? [String: Any]
                    if let parsedJSON = parsedJSON {
                        print("🔍 AI Generation: Successfully parsed JSON from textResponse")
                        print("🔍 AI Generation: Parsed JSON keys: \(parsedJSON.keys)")

                        // Check if the parsed JSON has a "questions" array
                        if let questionsArray = parsedJSON["questions"] as? [[String: Any]] {
                            print("🔍 AI Generation: Found questions array with \(questionsArray.count) items")
                            questionsData = questionsArray
                        } else if let questionsArray = parsedJSON as? [[String: Any]] {
                            // In case the response is directly an array of questions
                            print("🔍 AI Generation: Response is directly an array with \(questionsArray.count) items")
                            questionsData = questionsArray
                        } else {
                            print("🔍 AI Generation: No questions array found in parsed JSON")
                            print("🔍 AI Generation: Available keys in parsed JSON: \(Array(parsedJSON.keys))")
                        }
                    } else {
                        print("🔍 AI Generation: Parsed JSON is not a dictionary")
                    }
                } catch {
                    print("🔍 AI Generation: JSON parsing error: \(error)")
                    print("🔍 AI Generation: Attempting to parse first 500 chars: \(cleanedResponse.prefix(500))")

                    // Try to identify the exact problematic character
                    if let errorInfo = (error as NSError).userInfo["NSJSONSerializationErrorIndex"] as? Int {
                        let startIndex = max(0, errorInfo - 20)
                        let endIndex = min(cleanedResponse.count, errorInfo + 20)
                        let problemArea = String(cleanedResponse.dropFirst(startIndex).prefix(endIndex - startIndex))
                        print("🔍 AI Generation: Problem area around index \(errorInfo): '\(problemArea)'")

                        // Show the exact character at the error index
                        if errorInfo < cleanedResponse.count {
                            let problemChar = cleanedResponse[cleanedResponse.index(cleanedResponse.startIndex, offsetBy: errorInfo)]
                            print("🔍 AI Generation: Problem character at index \(errorInfo): '\(problemChar)' (Unicode: \\u{\(String(problemChar.unicodeScalars.first!.value, radix: 16))})")
                        }
                    }
                }
            } else {
                print("🔍 AI Generation: Failed to convert cleaned response to Data")
            }
        } else if let questionsArray = json["questions"] as? [[String: Any]] {
            questionsData = questionsArray
        } else if let questionsArray = json as? [[String: Any]] {
            questionsData = questionsArray
        }

        print("🤖 AI Generation: Found \(questionsData.count) question dictionaries")

        var questions: [Question] = []

        for (index, questionDict) in questionsData.enumerated() {
            do {
                let question = try parseQuestion(from: questionDict, index: index)
                questions.append(question)
            } catch {
                print("❌ AI Generation: Failed to parse question \(index): \(error)")
                // Continue with other questions instead of failing completely
            }
        }

        print("✅ AI Generation: Successfully created \(questions.count) questions")
        return questions
    }

    private func parseQuestion(from dict: [String: Any], index: Int) throws -> Question {
        guard let questionText = dict["question"] as? String ?? dict["questionText"] as? String else {
            throw NSError(domain: "AIQuestionGenerator", code: 500, userInfo: [
                NSLocalizedDescriptionKey: "Missing question text for question \(index)"
            ])
        }

        // Parse options
        var options: [String] = []
        if let optionsArray = dict["options"] as? [String] {
            options = optionsArray
        } else if let optionsDict = dict["options"] as? [String: String] {
            // Convert dictionary to array (A, B, C, D order)
            let sortedKeys = optionsDict.keys.sorted()
            options = sortedKeys.compactMap { optionsDict[$0] }
        }

        // Parse correct answer
        var correctAnswerIndex = 0
        if let correctAnswer = dict["correct_answer"] as? String {
            // If it's a letter (A, B, C, D), convert to index
            if let firstChar = correctAnswer.uppercased().first,
               let index = "ABCD".firstIndex(of: firstChar) {
                correctAnswerIndex = "ABCD".distance(from: "ABCD".startIndex, to: index)
            } else if let index = Int(correctAnswer) {
                correctAnswerIndex = index
            }
        } else if let index = dict["correct_answer_index"] as? Int {
            correctAnswerIndex = index
        } else if let index = dict["correctAnswerIndex"] as? Int {
            correctAnswerIndex = index
        }

        // Parse points
        let points = dict["points"] as? Int ?? 1

        // Normalize LaTeX in question text and options
        let normalizedQuestionText = normalizeLaTeX(questionText)
        let normalizedOptions = options.map { normalizeLaTeX($0) }

        return Question(
            questionText: normalizedQuestionText,
            questionType: .multipleChoice,
            options: normalizedOptions.isEmpty ? nil : normalizedOptions,
            correctAnswerIndex: correctAnswerIndex,
            points: points
        )
    }

    private func extractJSONFromMarkdown(_ text: String) -> String {
        // Check if the text contains markdown code blocks
        if text.contains("```json") && text.contains("```") {
            // Extract content between ```json and the next ```
            // Updated pattern to be more flexible with whitespace and newlines
            let pattern = "```json\\s*(.*?)\\s*```"
            let options: NSRegularExpression.Options = [.dotMatchesLineSeparators]

            if let regex = try? NSRegularExpression(pattern: pattern, options: options),
               let match = regex.firstMatch(in: text, options: [], range: NSRange(location: 0, length: text.utf16.count)) {

                if let range = Range(match.range(at: 1), in: text) {
                    let extracted = String(text[range]).trimmingCharacters(in: .whitespacesAndNewlines)
                    print("🔍 AI Generation: Extracted JSON from markdown: \(extracted.prefix(100))...")

                    // Fix LaTeX escape sequences in the JSON
                    let fixedJSON = fixLatexEscapeSequencesInJSON(extracted)
                    print("🔍 AI Generation: Fixed LaTeX escapes: \(fixedJSON.prefix(100))...")
                    return fixedJSON
                }
            }
        }

        // If no markdown code blocks found or extraction failed, return the original text
        print("🔍 AI Generation: No markdown code blocks found, returning original text")
        return text
    }

    private func fixLatexEscapeSequencesInJSON(_ jsonString: String) -> String {
        var fixed = jsonString

        print("🔧 Starting JSON LaTeX fix. Original length: \(fixed.count)")
        print("🔧 Sample input: \(fixed.prefix(200))")

        // The core issue: JSON doesn't recognize LaTeX escape sequences like \c in \circ
        // We need to escape ALL backslashes that are followed by letters (LaTeX commands)
        // regardless of whether they appear to be "already escaped" or not

        // Use regex to find and replace all LaTeX commands
        // Pattern: backslash followed by one or more letters
        let latexPattern = #"\\([a-zA-Z]+)"#

        do {
            let regex = try NSRegularExpression(pattern: latexPattern, options: [])
            let range = NSRange(fixed.startIndex..<fixed.endIndex, in: fixed)

            // Count matches before replacement
            let matches = regex.matches(in: fixed, options: [], range: range)
            print("🔧 Found \(matches.count) LaTeX commands to escape")

            // Replace all LaTeX commands with properly escaped versions
            fixed = regex.stringByReplacingMatches(
                in: fixed,
                options: [],
                range: range,
                withTemplate: "\\\\\\\\$1"  // Replace \command with \\command
            )

            print("🔧 Applied regex fix - escaped \(matches.count) LaTeX commands")

        } catch {
            print("🔧 Regex failed: \(error), falling back to manual replacement")

            // Fallback: manually replace the most common LaTeX commands
            let commonCommands = [
                "\\circ", "\\deg", "\\times", "\\div", "\\cdot", "\\pm", "\\mp",
                "\\frac", "\\sqrt", "\\angle", "\\triangle", "\\alpha", "\\beta",
                "\\gamma", "\\delta", "\\theta", "\\pi", "\\sigma", "\\phi"
            ]

            for command in commonCommands {
                let beforeCount = fixed.components(separatedBy: command).count - 1
                if beforeCount > 0 {
                    fixed = fixed.replacingOccurrences(of: command, with: "\\\\" + command.dropFirst())
                    print("🔧 Manually fixed \(beforeCount) instances of '\(command)'")
                }
            }
        }

        print("🔧 Final fixed JSON length: \(fixed.count) characters")
        print("🔧 Sample output: \(fixed.prefix(200))")
        return fixed
    }

    // Test function to verify the fix works
    private func testJSONFix() {
        print("🧪 Testing JSON fix with regex approach...")

        // Test with the actual problematic pattern from the logs
        let testJSON = "{\"questionText\": \"What is $115^\\circ$?\"}"
        print("🧪 Original: '\(testJSON)'")

        let fixed = fixLatexEscapeSequencesInJSON(testJSON)
        print("🧪 Fixed: '\(fixed)'")

        // Verify it parses correctly
        if let data = fixed.data(using: .utf8) {
            do {
                let parsed = try JSONSerialization.jsonObject(with: data)
                print("✅ JSON parsing PASSED!")
                if let dict = parsed as? [String: String] {
                    print("✅ Parsed content: \(dict)")
                }
            } catch {
                print("❌ JSON parsing FAILED: \(error)")
            }
        }
    }
}

// MARK: - LaTeX Normalization Functions
/// Normalize LaTeX expressions to fix common issues from LLM responses
func normalizeLaTeX(_ text: String) -> String {
    var normalized = text

    // PRIORITY FIX: Handle escaped dollar currency patterns FIRST before other normalization
    // This fixes the issue where $\$10$ gets incorrectly processed
    // Convert $\$10$ -> $10, $\$5$ -> $5, etc.
    let escapedDollarPattern = #"\$\\\$(\d+(?:\.\d+)?)\$"#
    normalized = normalized.replacingOccurrences(
        of: escapedDollarPattern,
        with: "$$1",
        options: .regularExpression
    )

    // Step 1: Fix escaped backslashes in JSON (\\frac -> \frac)
    // BUT preserve escaped dollars that we haven't processed yet
    // Use a more specific pattern that doesn't affect currency
    let latexCommandPattern = #"\\\\([a-zA-Z]+)"#
    normalized = normalized.replacingOccurrences(
        of: latexCommandPattern,
        with: "\\$1",
        options: .regularExpression
    )

    // Step 2: Remove any stray quotes around LaTeX expressions
    normalized = normalized.replacingOccurrences(of: "\"$", with: "$")
    normalized = normalized.replacingOccurrences(of: "$\"", with: "$")

    // Step 3: Ensure proper spacing around operators
    normalized = normalized.replacingOccurrences(of: "\\pm", with: " \\pm ")
    normalized = normalized.replacingOccurrences(of: "\\mp", with: " \\mp ")
    normalized = normalized.replacingOccurrences(of: "\\times", with: " \\times ")
    normalized = normalized.replacingOccurrences(of: "\\div", with: " \\div ")
    normalized = normalized.replacingOccurrences(of: "\\cdot", with: " \\cdot ")

    // Clean up multiple spaces
    normalized = normalized.replacingOccurrences(of: "  +", with: " ", options: .regularExpression)

    // Step 4: Fix common fraction issues
    normalized = normalized.replacingOccurrences(of: "\\frac{([^}]+)}{([^}]+)}", with: "\\frac{$1}{$2}", options: .regularExpression)

    return normalized
}

struct ManualAddQuestionView: View {
    @Environment(\.dismiss) private var dismiss
    let onAdd: (Question) -> Void
    
    @State private var questionText = ""
    @State private var questionType: QuestionType = .multipleChoice
    @State private var options: [String] = ["", "", "", ""]
    @State private var correctAnswerIndex = 0
    @State private var points = 1
    @State private var showError = false
    @State private var errorMessage = ""
    
    private var validOptions: [String] {
        options.filter { !$0.isEmpty }
    }
    
    private var isAddButtonDisabled: Bool {
        if questionText.isEmpty {
            return true
        }
        
        // For multiple choice, need at least 2 options
        if questionType == .multipleChoice {
            return validOptions.count < 2
        }
        
        // For long question, just need question text
        return false
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section("Question Details") {
                    LaTeXTextEditor(
                        text: $questionText,
                        placeholder: "Question Text",
                        minLines: 3,
                        maxLines: 6
                    )
                    
                    Picker("Question Type", selection: $questionType) {
                        Text("Multiple Choice").tag(QuestionType.multipleChoice)
                        Text("Long Question").tag(QuestionType.longQuestion)
                    }
                    .onChange(of: questionType) { newType in
                        // Reset options when switching to long question
                        if newType == .longQuestion {
                            options = ["", "", "", ""]
                            correctAnswerIndex = 0
                        }
                    }
                }
                
                // Only show options section for multiple choice questions
                if questionType == .multipleChoice {
                    Section("Options") {
                        ForEach(0..<4) { index in
                            HStack(alignment: .bottom, spacing: 8) {
                                Text("\(Character(UnicodeScalar(65 + index)!))")
                                    .font(.system(size: 18))
                                    .foregroundColor(.gray)
                                    .frame(width: 20)
                                    .alignmentGuide(.bottom) { d in d[.bottom] }
                                LaTeXTextEditor(
                                    text: $options[index],
                                    placeholder: "Option \(index + 1)",
                                    minLines: 1,
                                    maxLines: 3
                                )
                                .alignmentGuide(.bottom) { d in d[.bottom] }
                            }
                        }
                    }
                }
                
                // Question Preview Section - Always visible, placed after Options
                Section("Question Preview") {
                    VStack(alignment: .leading, spacing: 16) {
                        if !questionText.isEmpty || validOptions.count >= 2 {
                            VStack(alignment: .leading, spacing: 12) {
                                // Question Text Preview (larger font)
                                if !questionText.isEmpty {
                                    QuestionTextRenderer(text: questionText, fontSize: 20)
                                }
                                
                                // Options Preview (larger font, same container)
                                if validOptions.count >= 2 {
                                    ForEach(Array(validOptions.enumerated()), id: \.offset) { index, option in
                                        HStack(alignment: .top, spacing: 8) {
                                            Text("\(Character(UnicodeScalar(65 + index)!))")
                                                .font(.system(size: 18))
                                                .fontWeight(.medium)
                                                .foregroundColor(.gray)
                                                .frame(width: 20, alignment: .leading)

                                            QuestionTextRenderer(text: option, fontSize: 18)
                                                .frame(maxWidth: .infinity, alignment: .leading)
                                        }
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                    }
                                }
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 12)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                            
                            Text("This is how students will see the question with rendered mathematical expressions")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .italic()
                        } else {
                            VStack(spacing: 8) {
                                Image(systemName: "eye")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                                Text("Enter question and options above to see preview")
                                    .font(.subheadline)
                                    .foregroundColor(.gray)
                                    .multilineTextAlignment(.center)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 20)
                        }
                    }
                }
                
                Section("Answer & Scoring") {
                    // Only show correct answer picker for multiple choice questions
                    if questionType == .multipleChoice {
                        if validOptions.count >= 2 {
                            let options = validOptions
                            Picker("Correct Answer", selection: $correctAnswerIndex) {
                                ForEach(0..<options.count, id: \.self) { index in
                                    HStack {
                                        Text("\(Character(UnicodeScalar(65 + index)!))")
                                        QuestionTextRenderer(text: options[index], fontSize: 16)
                                    }.tag(index)
                                }
                            }
                        } else {
                            Text("Add at least 2 options to select correct answer")
                                .foregroundColor(.gray)
                        }
                    } else {
                        Text("Long questions will be graded manually")
                            .foregroundColor(.gray)
                            .font(.caption)
                    }
                    
                    Stepper("Points: \(points)", value: $points, in: 1...10)
                }
                
                Section {
                    Button(action: validateAndAddQuestion) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                            Text("Add Question")
                        }
                    }
                    .disabled(isAddButtonDisabled)
                }
            }
            .navigationTitle("Add Question")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                }
            )
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    private func validateAndAddQuestion() {
        // Validate the question
        if questionText.isEmpty {
            errorMessage = "Please enter a question"
            showError = true
            return
        }
        
        // Validate based on question type
        if questionType == .multipleChoice {
            if validOptions.count < 2 {
                errorMessage = "Please add at least 2 options for multiple choice questions"
                showError = true
                return
            }
        }
        
        // Create and add the question
        let question = Question(
            questionText: normalizeLaTeX(questionText),
            questionType: questionType,
            options: questionType == .multipleChoice ? validOptions.map { normalizeLaTeX($0) } : nil,
            correctAnswerIndex: questionType == .multipleChoice ? correctAnswerIndex : 0,
            points: points
        )
        onAdd(question)
        dismiss()
    }
}

// MARK: - AI Question Generation View
struct AIQuestionGenerationView: View {
    @Environment(\.dismiss) private var dismiss
    let topic: String
    let subtopic: String
    let onGenerate: ([Question]) -> Void

    @State private var multipleChoiceCount = 5
    @State private var longAnswerCount = 0
    @State private var difficultyLevel: DifficultyLevel = .intermediate
    @State private var customPrompt = ""
    @State private var isGeneratingQuestions = false
    @State private var showError = false
    @State private var errorMessage = ""

    enum DifficultyLevel: String, CaseIterable {
        case beginner = "Beginner"
        case intermediate = "Intermediate"
        case advanced = "Advanced"

        var apiValue: String {
            return self.rawValue.lowercased()
        }
    }

    private var totalQuestions: Int {
        return multipleChoiceCount + longAnswerCount
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Form
                Form {
                    Section("Difficulty Level") {
                        Picker("Difficulty", selection: $difficultyLevel) {
                            ForEach(DifficultyLevel.allCases, id: \.self) { level in
                                Text(level.rawValue).tag(level)
                            }
                        }
                        .pickerStyle(.segmented)
                    }

                    Section("Question Distribution") {
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Text("Multiple Choice Questions:")
                                Spacer()
                                TextField("5", value: $multipleChoiceCount, format: .number)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                    .frame(width: 60)
                                    .keyboardType(.numberPad)
                                    .onChange(of: multipleChoiceCount) { newValue in
                                        if newValue < 0 {
                                            multipleChoiceCount = 0
                                        } else if newValue > 20 {
                                            multipleChoiceCount = 20
                                        }
                                    }
                            }

                            HStack {
                                Text("Long Answer Questions:")
                                Spacer()
                                HStack(spacing: 8) {
                                    TextField("0", value: $longAnswerCount, format: .number)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .frame(width: 60)
                                        .keyboardType(.numberPad)
                                        .disabled(true)
                                        .opacity(0.5)

                                    Text("Coming Soon")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.orange.opacity(0.1))
                                        .cornerRadius(4)
                                }
                            }

                            HStack {
                                Text("Total Questions:")
                                Spacer()
                                Text("\(totalQuestions)")
                                    .font(.title2)
                                    .fontWeight(.semibold)
                                    .foregroundColor(.black)
                                    .frame(minWidth: 40)
                            }
                        }
                    }

                    Section(header: Text("Custom Instructions (Optional)")) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Provide specific requirements or examples for the AI to follow:")
                                .font(.caption)
                                .foregroundColor(.gray)

                            TextEditor(text: $customPrompt)
                                .frame(minHeight: 100)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                        }

                        Text("Example: 'Include word problems involving real-world scenarios' or 'Focus on algebraic manipulation with fractions'")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .italic()
                    }


                // Generate Button Section
                Section {
                    Button(action: generateAIQuestions) {
                        HStack {
                            if isGeneratingQuestions {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "wand.and.stars")
                                Text("Generate Questions")
                            }
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            isGeneratingQuestions || totalQuestions == 0 ?
                            Color.gray :
                            Color.blue
                        )
                        .cornerRadius(10)
                    }
                    .disabled(isGeneratingQuestions || totalQuestions == 0)

                    if isGeneratingQuestions {
                        Text("Generating questions... This may take a few seconds.")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.top, 8)
                    }

                    if totalQuestions == 0 {
                        Text("Please select at least one question type to generate")
                            .font(.caption)
                            .foregroundColor(.orange)
                            .multilineTextAlignment(.center)
                            .padding(.top, 8)
                    }
                }
                }
            }
            .toolbar {
                ToolbarItem(placement: .principal) {
                    HStack(spacing: 8) {
                        Image(systemName: "wand.and.stars")
                            .foregroundColor(.blue)
                        Text("AI Generation")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                }
            }
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                }
            )
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }

    private var canGenerate: Bool {
        totalQuestions > 0
    }

    private func generateAIQuestions() {
        guard canGenerate else { return }

        isGeneratingQuestions = true

        Task {
            do {
                let questions = try await AIQuestionGenerator.shared.generateQuestions(
                    topic: topic,
                    subtopic: subtopic.isEmpty ? nil : subtopic,
                    numberOfQuestions: multipleChoiceCount,
                    difficulty: difficultyLevel.apiValue,
                    questionTypes: [QuestionType.multipleChoice],
                    customPrompt: customPrompt.isEmpty ? nil : customPrompt
                )

                await MainActor.run {
                    self.isGeneratingQuestions = false
                    self.onGenerate(questions)
                    self.dismiss()
                }
            } catch {
                await MainActor.run {
                    self.isGeneratingQuestions = false
                    self.errorMessage = "Failed to generate questions: \(error.localizedDescription)"
                    self.showError = true
                }
            }
        }
    }
}

struct EditQuestionWrapperView: View {
    @Environment(\.dismiss) private var dismiss
    let question: Question
    let onSave: (Question) -> Void

    @State private var questionText: String
    @State private var questionType: QuestionType
    @State private var options: [String]
    @State private var correctAnswerIndex: Int
    @State private var points: Int
    @State private var showError = false
    @State private var errorMessage = ""

    init(question: Question, onSave: @escaping (Question) -> Void) {
        self.question = question
        self.onSave = onSave
        self._questionText = State(initialValue: question.questionText)
        self._questionType = State(initialValue: question.questionType)
        self._options = State(initialValue: question.options ?? ["", "", "", ""])
        self._correctAnswerIndex = State(initialValue: question.correctAnswerIndex)
        self._points = State(initialValue: question.points)
    }

    private var validOptions: [String] {
        return options.filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
    }

    private var canSave: Bool {
        let trimmedText = questionText.trimmingCharacters(in: .whitespacesAndNewlines)

        switch questionType {
        case .multipleChoice:
            return !trimmedText.isEmpty && validOptions.count >= 2 && correctAnswerIndex < validOptions.count
        case .longQuestion, .shortAnswer, .longAnswer, .essay, .fillInBlank, .trueFalse:
            return !trimmedText.isEmpty
        }
    }

    var body: some View {
        NavigationStack {
            Form {
                Section("Question Details") {
                    TextField("Question Text", text: $questionText, axis: .vertical)
                        .lineLimit(3...10)

                    Picker("Question Type", selection: $questionType) {
                        Text("Multiple Choice").tag(QuestionType.multipleChoice)
                        Text("Long Question").tag(QuestionType.longQuestion)
                        Text("Short Answer").tag(QuestionType.shortAnswer)
                        Text("Essay").tag(QuestionType.essay)
                        Text("True/False").tag(QuestionType.trueFalse)
                    }
                    .onChange(of: questionType) { newType in
                        if newType == .longQuestion {
                            // Clear options for long questions
                            options = ["", "", "", ""]
                            correctAnswerIndex = 0
                        }
                    }

                    Stepper("Points: \(points)", value: $points, in: 1...100)
                }

                if questionType == .multipleChoice {
                    Section("Answer Options") {
                        ForEach(0..<4, id: \.self) { index in
                            HStack {
                                Text("\(Character(UnicodeScalar(65 + index)!)).")
                                    .font(.headline)
                                    .foregroundColor(.gray)
                                    .frame(width: 20)

                                TextField("Option \(index + 1)", text: $options[index])

                                Button(action: {
                                    correctAnswerIndex = index
                                }) {
                                    Image(systemName: correctAnswerIndex == index ? "checkmark.circle.fill" : "circle")
                                        .foregroundColor(correctAnswerIndex == index ? .green : .gray)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }

                        Text("Tap the circle to mark the correct answer")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }
            .navigationTitle("Edit Question")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    print("🔧 DEBUG: EditQuestionWrapperView - Cancel button tapped")
                    dismiss()
                },
                trailing: Button("Save") {
                    print("🔧 DEBUG: EditQuestionWrapperView - Save button tapped")
                    saveQuestion()
                }
                .disabled(!canSave)
            )
            .alert("Error", isPresented: $showError) {
                Button("OK", role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
    }

    private func saveQuestion() {
        print("🔧 DEBUG: EditQuestionWrapperView - saveQuestion called")
        let trimmedText = questionText.trimmingCharacters(in: .whitespacesAndNewlines)

        guard !trimmedText.isEmpty else {
            errorMessage = "Please enter a question"
            showError = true
            return
        }

        let finalOptions: [String]?
        let finalCorrectIndex: Int

        switch questionType {
        case .multipleChoice:
            guard validOptions.count >= 2 else {
                errorMessage = "Please provide at least 2 answer options"
                showError = true
                return
            }

            guard correctAnswerIndex < validOptions.count else {
                errorMessage = "Please select a correct answer"
                showError = true
                return
            }

            finalOptions = validOptions
            finalCorrectIndex = correctAnswerIndex

        case .longQuestion, .shortAnswer, .longAnswer, .essay, .fillInBlank, .trueFalse:
            finalOptions = nil
            finalCorrectIndex = 0
        }

        let updatedQuestion = Question(
            id: question.id, // Keep the same ID
            questionText: trimmedText,
            questionType: questionType,
            options: finalOptions,
            correctAnswerIndex: finalCorrectIndex,
            points: points
        )

        print("🔧 DEBUG: EditQuestionWrapperView - calling onSave with updated question")
        onSave(updatedQuestion)
        print("🔧 DEBUG: Dismissing EditQuestionWrapperView")
        dismiss()
    }
}

#Preview {
    EditQuestionWrapperView(
        question: Question(
            questionText: "What is 2 + 2?",
            questionType: .multipleChoice,
            options: ["3", "4", "5", "6"],
            correctAnswerIndex: 1,
            points: 10
        )
    ) { updatedQuestion in
        print("Updated question: \(updatedQuestion.questionText)")
    }
}
