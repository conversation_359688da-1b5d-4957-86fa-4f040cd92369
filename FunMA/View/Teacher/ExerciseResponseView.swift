import SwiftUI
import PencilKit

// MARK: - Student API Response Models

struct StudentDetailsResponse: Codable {
    let student: StudentDetails
    let school: SchoolInfo
}

struct StudentDetails: Codable {
    let id: String
    let username: String
    let firstName: String
    let lastName: String
    let role: String
    let credit: Int
    let schoolId: String
    let classrooms: [StudentClassroomInfo]
    let currentClasses: [StudentCurrentClass]
    
    var fullName: String {
        return "\(firstName) \(lastName)".trimmingCharacters(in: .whitespaces)
    }
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case username
        case firstName
        case lastName
        case role
        case credit
        case schoolId = "school_id"
        case classrooms
        case currentClasses
    }
}

struct StudentCurrentClass: Codable {
    let name: String
    let grade: String
    let teacherName: String
}

struct ExerciseResponseView: View {
    let exercise: Exercise
    let classroom: Classroom?
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    @Environment(\.dismiss) private var dismiss
    
    @State private var submissions: [StudentSubmission] = []
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var studentNames: [String: String] = [:] // Cache for student names
    // NEW: State for selected submission and sheet
    @State private var selectedSubmission: StudentSubmission? = nil
    // NEW: State for showing review sheet
    @State private var showReviewSheet: Bool = false

    
    // Filter submissions by classroom if provided
    private var filteredSubmissions: [StudentSubmission] {
        guard let classroom = classroom else { return submissions }
        let classroomStudentIds = Set(classroom.students.map { $0.id })
        return submissions.filter { classroomStudentIds.contains($0.studentId) }
    }
    
    // Group submissions by completion status
    private var submissionStats: (submitted: Int, notSubmitted: Int, late: Int) {
        let submitted = filteredSubmissions.filter { $0.endTime != nil && !isLateSubmission($0) }.count
        let late = filteredSubmissions.filter { $0.endTime != nil && isLateSubmission($0) }.count
        let totalStudents = classroom?.students.count ?? 0
        let notSubmitted = max(0, totalStudents - submitted - late)
        
        return (submitted: submitted, notSubmitted: notSubmitted, late: late)
    }
    
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Header Section
                exerciseHeaderSection
                
                Divider()
                
                // Stats Section
                statsSection
                
                Divider()
                
                // Submissions List
                if isLoading {
                    loadingView
                } else if let errorMessage = errorMessage {
                    errorView(errorMessage)
                } else if filteredSubmissions.isEmpty {
                    emptyStateView
                } else {
                    submissionsList
                }
            }
            .navigationTitle("Exercise Responses")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            print("🎯 ExerciseResponseView: View appeared for exercise '\(exercise.title)'")
            print("🎯 ExerciseResponseView: isLoading = \(isLoading)")
            print("🎯 ExerciseResponseView: errorMessage = \(errorMessage ?? "nil")")
            print("🎯 ExerciseResponseView: submissions count = \(submissions.count)")
            print("🎯 ExerciseResponseView: filteredSubmissions count = \(filteredSubmissions.count)")
            print("🎯 ExerciseResponseView: classroom = \(classroom?.name ?? "nil")")
        }
        .task {
            await loadSubmissions()
        }
    }
    
    // MARK: - Header Section
    
    private var exerciseHeaderSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(exercise.title)
                        .font(.title)
                        .fontWeight(.bold)

                    Text(exercise.topic)
                        .font(.headline)
                        .foregroundColor(.blue)

                    if !exercise.subtopic.isEmpty {
                        Text(exercise.subtopic)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(exercise.questions.count) Questions")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                        .lineLimit(1)
                }
            }
            
            // Exercise Info
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(.orange)
                        Text("Due: \(exercise.dueDate.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    HStack {
                        Image(systemName: "calendar.badge.plus")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text("Created: \(exercise.createdAt.formatted(date: .abbreviated, time: .omitted))")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    if let classroom = classroom {
                        HStack {
                            Image(systemName: "building.2")
                                .font(.caption)
                                .foregroundColor(.purple)
                            Text("Classroom: \(classroom.name)")
                                .font(.caption)
                                .foregroundColor(.purple)
                        }
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Stats Section
    
    private var statsSection: some View {
        HStack(spacing: 20) {
            SubmissionStatItem(
                value: submissionStats.submitted,
                label: "Submitted",
                color: .green,
                icon: "checkmark.circle.fill"
            )
            
            SubmissionStatItem(
                value: submissionStats.late,
                label: "Late",
                color: .orange,
                icon: "clock.fill"
            )
            
            SubmissionStatItem(
                value: submissionStats.notSubmitted,
                label: "Not Submitted",
                color: .red,
                icon: "xmark.circle.fill"
            )
            
            SubmissionStatItem(
                value: filteredSubmissions.count,
                label: "Total Responses",
                color: .blue,
                icon: "doc.text.fill"
            )
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Content Views
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Loading submissions...")
                .font(.subheadline)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private func errorView(_ message: String) -> some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("Error Loading Submissions")
                .font(.headline)
                .fontWeight(.semibold)
            
            Text(message)
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Try Again") {
                Task {
                    await loadSubmissions()
                }
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "doc.text.below.ecg")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Submissions Yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Students haven't submitted their responses yet")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private var submissionsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredSubmissions) { submission in
                    SubmissionCard(
                        submission: submission,
                        exercise: exercise,
                        studentName: getStudentNameFromCache(for: submission.studentId)
                    )
                    .onTapGesture {
                        print("[ExerciseResponseView] Card tapped for submission id: \(submission.id)")
                        selectedSubmission = submission
                    }
                }
            }
            .padding()
        }
        .refreshable {
            await loadSubmissions(forceRefresh: true)
        }
        .background(Color(.systemGroupedBackground))
        // Use .sheet(item:) for robust modal presentation
        .fullScreenCover(item: $selectedSubmission) { submission in
            ManualGradingSheet(
                exercise: exercise,
                submission: submission,
                viewModel: exerciseViewModel,
                onDismiss: { 
                    selectedSubmission = nil
                    // Refresh submissions to get updated grades
                    Task {
                        await loadSubmissions()
                    }
                }
            )
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadSubmissions(forceRefresh: Bool = false) async {
        isLoading = true
        errorMessage = nil
        do {
            let loadedSubmissions = try await exerciseViewModel.getStudentSubmissions(for: exercise.id, forceRefresh: forceRefresh)
            submissions = loadedSubmissions
            print("✅ ExerciseResponseView: Loaded \(loadedSubmissions.count) submissions for exercise '\(exercise.title)'")
            if let classroom = classroom {
                print("🏫 ExerciseResponseView: Classroom '\(classroom.name)' has \(classroom.students.count) students")
                for student in classroom.students {
                    print("   👤 Student: '\(student.name)' (ID: '\(student.id))")
                }
            } else {
                print("⚠️ ExerciseResponseView: No classroom provided")
            }
            await preloadStudentNames(for: loadedSubmissions)
            for (index, submission) in loadedSubmissions.enumerated() {
                let studentName = getStudentNameFromCache(for: submission.studentId)
                print("📝 Submission \(index + 1): Student ID '\(submission.studentId)' -> Name: '\(studentName)'")
            }
        } catch {
            errorMessage = "Failed to load submissions: \(error.localizedDescription)"
            print("❌ ExerciseResponseView: Failed to load submissions: \(error.localizedDescription)")
        }
        isLoading = false
    }
    
    private func getStudentName(for studentId: String) -> String {
        // Debug logging
        print("🔍 Looking for student name for ID: '\(studentId)'")
        
        guard let classroom = classroom else {
            print("⚠️ No classroom available - returning 'Unknown Student'")
            return "Unknown Student"
        }
        
        // Try to find student in classroom
        if let student = classroom.students.first(where: { $0.id == studentId }) {
            print("✅ Found student: '\(student.name)' for ID: '\(studentId)'")
            return student.name
        }
        
        // Alternative: Try to match with username if available
        if let student = classroom.students.first(where: { $0.username == studentId }) {
            print("✅ Found student by username: '\(student.name)' for ID: '\(studentId)'")
            return student.name
        }
        
        // Debug: Log all available student IDs for comparison
        print("⚠️ Student ID '\(studentId)' not found in classroom. Available students:")
        for student in classroom.students {
            print("   - ID: '\(student.id)', Username: '\(student.username)', Name: '\(student.name)'")
        }
        
        return "Unknown Student (\(studentId.prefix(8))...)"
    }
    
    // Fallback method to fetch student names from backend
    private func fetchStudentName(for studentId: String) async -> String {
        // Check cache first
        if let cachedName = studentNames[studentId] {
            return cachedName
        }
        
        do {
            // Try to fetch student info from backend using the correct student API endpoint
            let endpoint = "student/?student_id=\(studentId)"
            let response: StudentDetailsResponse = try await UserManager.shared.api.get(endpoint)
            
            let student = response.student
            let name = student.fullName.isEmpty ? student.username : student.fullName
            
            // Cache the result
            await MainActor.run {
                studentNames[studentId] = name
            }
            print("✅ Fetched student name from backend: '\(name)' for ID: '\(studentId)'")
            return name
        } catch {
            print("❌ Failed to fetch student name from backend for ID '\(studentId)': \(error.localizedDescription)")
        }
        
        return "Unknown Student (\(studentId.prefix(8))...)"
    }
    
    // Async version of getStudentName that can fetch from backend
    private func getStudentNameAsync(for studentId: String) async -> String {
        // First try the regular method (classroom lookup)
        let localName = getStudentName(for: studentId)
        
        // If it's an "Unknown Student", try fetching from backend
        if localName.hasPrefix("Unknown Student") {
            return await fetchStudentName(for: studentId)
        }
        
        return localName
    }
    
    private func isLateSubmission(_ submission: StudentSubmission) -> Bool {
        guard let endTime = submission.endTime else { return false }
        return endTime > exercise.dueDate
    }
    
    private func preloadStudentNames(for submissions: [StudentSubmission]) async {
        // Collect all unknown student IDs first
        var unknownStudentIds: [String] = []
        
        for submission in submissions {
            let studentName = getStudentName(for: submission.studentId)
            if studentName.hasPrefix("Unknown Student") {
                unknownStudentIds.append(submission.studentId)
            }
        }
        
        // Fetch all unknown student names concurrently and wait for completion
        if !unknownStudentIds.isEmpty {
            print("📥 Preloading \(unknownStudentIds.count) unknown student names...")
            
            await withTaskGroup(of: Void.self) { group in
                for studentId in unknownStudentIds {
                    group.addTask {
                        await self.fetchStudentName(for: studentId)
                    }
                }
            }
            
            print("✅ Completed preloading student names")
        }
    }
    
    private func getStudentNameFromCache(for studentId: String) -> String {
        // First check our cache (should be populated by preloading)
        if let cachedName = studentNames[studentId] {
            print("✅ Using cached student name: '\(cachedName)' for ID: '\(studentId)'")
            return cachedName
        }
        
        // Then try the regular classroom lookup
        let classroomName = getStudentName(for: studentId)
        
        // If found in classroom, return it
        if !classroomName.hasPrefix("Unknown Student") {
            return classroomName
        }
        
        // If we reach here, preloading might have failed or this is a new student
        print("⚠️ Student name not found in cache or classroom for ID: '\(studentId)'")
        return "Unknown Student"
    }
}

// MARK: - Submission Stat Item Component

struct SubmissionStatItem: View {
    let value: Int
    let label: String
    let color: Color
    let icon: String

    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(color)

                Text("\(value) \(label)")
                    .font(.headline)
                    .fontWeight(.bold)
                    .foregroundColor(color)
                    .lineLimit(1)
            }
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - Submission Card Component

struct SubmissionCard: View {
    let submission: StudentSubmission
    let exercise: Exercise
    let studentName: String
    
    private var submissionStatus: (text: String, color: Color, icon: String) {
        if let endTime = submission.endTime {
            if endTime > exercise.dueDate {
                return ("Late", .orange, "clock.fill")
            } else {
                return ("Submitted", .green, "checkmark.circle.fill")
            }
        } else {
            return ("In Progress", .blue, "hourglass")
        }
    }
    
    private var scoreText: String {
        // Use submission's earnedPoints and totalPoints if available
        if let earnedPoints = submission.earnedPoints,
           let totalPoints = submission.totalPoints,
           totalPoints > 0 {
            let percentage = (Double(earnedPoints) / Double(totalPoints)) * 100
            return String(format: "%.1f%%", percentage)
        }
        
        // Fallback to local calculation for submissions without earnedPoints/totalPoints
        let calculatedScore = calculateLocalScore()
        if calculatedScore >= 0 {
            return String(format: "%.1f%%", calculatedScore)
        } else if submission.endTime != nil {
            return "Pending"
        } else {
            return "In Progress"
        }
    }
    
    // Calculate score locally by considering both automatic and manual grading
    private func calculateLocalScore() -> Double {
        var totalPoints = 0.0
        var earnedPoints = 0.0
        
        for question in exercise.questions {
            totalPoints += Double(question.points)
            if let answer = submission.answers.first(where: { $0.questionId == question.id }) {
                // Use manually graded points if available
                if let manualPoints = answer.pointsEarned {
                    earnedPoints += manualPoints
                } else if question.questionType == .multipleChoice {
                    // For multiple choice, check if answer is correct
                    if let answerIndex = Int(answer.answer),
                       answerIndex == question.correctAnswerIndex {
                        earnedPoints += Double(question.points)
                    }
                }
                // For long questions without manual grading, no points are awarded
            }
        }
        
        return totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : -1
    }
    
    var body: some View {
        VStack {
            HStack(spacing: 12) {
                // Student Avatar
                Circle()
                    .fill(submissionStatus.color.opacity(0.2))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Text(studentName.prefix(1).uppercased())
                            .font(.title2)
                            .fontWeight(.semibold)
                            .foregroundColor(submissionStatus.color)
                    )
                
                // Submission Info
                VStack(alignment: .leading, spacing: 4) {
                    Text(studentName)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        Image(systemName: submissionStatus.icon)
                            .font(.caption)
                            .foregroundColor(submissionStatus.color)
                        
                        Text(submissionStatus.text)
                            .font(.caption)
                            .foregroundColor(submissionStatus.color)
                    }
                    
                    if let endTime = submission.endTime {
                        Text("Submitted: \(endTime.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.gray)
                    } else {
                        Text("Started: \(submission.startTime.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Spacer()
                
                // Score and chevron
                VStack(alignment: .trailing, spacing: 4) {
                    Text(scoreText)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("Score")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        }
    }
}

// MARK: - Student Submission Detail View

struct StudentSubmissionDetailView: View {
    let submission: StudentSubmission
    let exercise: Exercise
    let studentName: String
    @Environment(\.dismiss) private var dismiss
    
    private var submissionStats: (correct: Int, incorrect: Int, unanswered: Int, totalPoints: Double, earnedPoints: Double) {
        var correct = 0
        var incorrect = 0
        var unanswered = 0
        var totalPoints = 0.0
        var earnedPoints = 0.0
        
        for question in exercise.questions {
            totalPoints += Double(question.points)
            
            if let answer = submission.answers.first(where: { $0.questionId == question.id }) {
                if let answerIndex = Int(answer.answer),
                   answerIndex == question.correctAnswerIndex {
                    correct += 1
                    earnedPoints += Double(question.points)
                } else {
                    incorrect += 1
                }
            } else {
                unanswered += 1
            }
        }
        
        return (correct: correct, incorrect: incorrect, unanswered: unanswered, totalPoints: totalPoints, earnedPoints: earnedPoints)
    }
    
    private var finalScore: Double {
        let stats = submissionStats
        return stats.totalPoints > 0 ? (stats.earnedPoints / stats.totalPoints) * 100 : 0
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header Section
                    headerSection
                    
                    // Stats Section
                    statsSection
                    
                    // Questions and Answers
                    questionsSection
                }
                .padding()
            }
            .navigationTitle("\(studentName)'s Submission")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            // Student Avatar
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 80, height: 80)
                .overlay(
                    Text(studentName.prefix(1).uppercased())
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                )
            
            Text(studentName)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(exercise.title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            // Submission timing
            VStack(spacing: 4) {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.gray)
                    Text("Started: \(submission.startTime.formatted(date: .abbreviated, time: .shortened))")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                
                if let endTime = submission.endTime {
                    HStack {
                        Image(systemName: endTime > exercise.dueDate ? "exclamationmark.triangle" : "checkmark.circle")
                            .foregroundColor(endTime > exercise.dueDate ? .orange : .green)
                        Text("Submitted: \(endTime.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(endTime > exercise.dueDate ? .orange : .green)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }
    
    private var statsSection: some View {
        let stats = submissionStats
        
        return VStack(spacing: 16) {
            // Overall Score
            HStack {
                Text("Final Score")
                    .font(.headline)
                Spacer()
                Text(String(format: "%.1f%%", finalScore))
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(finalScore >= 70 ? .green : finalScore >= 50 ? .orange : .red)
            }
            
            Divider()
            
            // Stats Grid
            HStack(spacing: 20) {
                StatItemView(
                    value: stats.correct,
                    label: "Correct",
                    color: .green,
                    icon: "checkmark.circle.fill"
                )
                
                StatItemView(
                    value: stats.incorrect,
                    label: "Incorrect",
                    color: .red,
                    icon: "record.circle.fill"
                )
                
                StatItemView(
                    value: stats.unanswered,
                    label: "Unanswered",
                    color: .gray,
                    icon: "minus.circle.fill"
                )
            }
            
            // Points breakdown
            HStack {
                Text("Points")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                Spacer()
                Text("\(Int(stats.earnedPoints)) / \(Int(stats.totalPoints))")
                    .font(.subheadline)
                    .fontWeight(.medium)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    private var questionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Question Breakdown")
                .font(.headline)
                .padding(.horizontal)
            
            ForEach(Array(exercise.questions.enumerated()), id: \.element.id) { index, question in
                QuestionDetailCard(
                    question: question,
                    questionNumber: index + 1,
                    studentAnswer: submission.answers.first(where: { $0.questionId == question.id })
                )
            }
        }
    }
}

// MARK: - Supporting Views

struct StatItemView: View {
    let value: Int
    let label: String
    let color: Color
    let icon: String

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text("\(value) \(label)")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(color)
                .lineLimit(1)
        }
        .frame(maxWidth: .infinity)
    }
}

struct QuestionDetailCard: View {
    let question: Question
    let questionNumber: Int
    let studentAnswer: QuestionSubmission?
    
    private var isCorrect: Bool {
        guard let studentAnswer = studentAnswer,
              let answerIndex = Int(studentAnswer.answer) else {
            return false
        }
        return answerIndex == question.correctAnswerIndex
    }
    
    private var answerStatus: (text: String, color: Color, icon: String) {
        guard let studentAnswer = studentAnswer else {
            return ("Not Answered", .gray, "minus.circle")
        }
        
        if isCorrect {
            return ("Correct", .green, "checkmark.circle.fill")
        } else {
            return ("Incorrect", .red, "record.circle.fill")
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Question header
            HStack {
                Text("Question \(questionNumber)")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                HStack(spacing: 4) {
                    Image(systemName: answerStatus.icon)
                        .foregroundColor(answerStatus.color)
                    Text(answerStatus.text)
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(answerStatus.color)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(answerStatus.color.opacity(0.1))
                .cornerRadius(8)
            }
            
            // Question text
            QuestionTextRenderer(text: question.questionText, fontSize: 16)
                .foregroundColor(.primary)
            
            // Answer options
            if let options = question.options {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                        AnswerOptionRow(
                            option: option,
                            index: index,
                            isCorrect: index == question.correctAnswerIndex,
                            isStudentAnswer: studentAnswer?.answer == String(index),
                            questionType: question.questionType
                        )
                    }
                }
            }
            
            // Points
            HStack {
                Text("Points:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                if isCorrect {
                    Text("\(question.points) / \(question.points)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                } else {
                    Text("0 / \(question.points)")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.red)
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

struct AnswerOptionRow: View {
    let option: String
    let index: Int
    let isCorrect: Bool
    let isStudentAnswer: Bool
    let questionType: QuestionType
    
    private var backgroundColor: Color {
        if isCorrect && isStudentAnswer {
            return .green.opacity(0.2)
        } else if isCorrect {
            return .green.opacity(0.1)
        } else if isStudentAnswer {
            return .red.opacity(0.2)
        } else {
            return Color(.systemGray6)
        }
    }
    
    private var borderColor: Color {
        if isCorrect && isStudentAnswer {
            return .green
        } else if isCorrect {
            return .green
        } else if isStudentAnswer {
            return .red
        } else {
            return .clear
        }
    }
    
    private var icon: String {
        if isCorrect && isStudentAnswer {
            return "checkmark.circle.fill"
        } else if isCorrect {
            return "checkmark.circle"
        } else if isStudentAnswer {
            return "record.circle.fill"  // Radio button style for student's incorrect answer
        } else {
            return "circle"
        }
    }
    
    private var iconColor: Color {
        if isCorrect && isStudentAnswer {
            return .green
        } else if isCorrect {
            return .green
        } else if isStudentAnswer {
            return .red
        } else {
            return .gray
        }
    }
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(iconColor)
                .font(.system(size: 16))
            
            QuestionTextRenderer(text: option, fontSize: 16)
                .foregroundColor(.primary)
            
            Spacer()
            
            if isStudentAnswer {
                Text("Student's Answer")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isCorrect ? .green : .red)
            }
            
            if isCorrect && !isStudentAnswer {
                Text("Correct Answer")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.green)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(backgroundColor)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(borderColor, lineWidth: borderColor == .clear ? 0 : 1)
        )
        .cornerRadius(8)
    }
}

// MARK: - Manual Grading Sheet

struct ManualGradingSheet: View {
    let exercise: Exercise
    let submission: StudentSubmission
    @ObservedObject var viewModel: ExerciseViewModel
    let onDismiss: () -> Void

    @State private var gradingInputs: [UUID: Double] = [:] // questionId -> points
    @State private var feedbackInputs: [UUID: String] = [:] // questionId -> feedback
    @State private var isSaving = false
    @State private var errorMessage: String?
    @State private var showSuccess = false

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    headerSection
                    ForEach(Array(exercise.questions.enumerated()), id: \.element.id) { pair in
                        let index = pair.offset
                        let question = pair.element
                        ManualGradingQuestionCard(
                            question: question,
                            questionNumber: index + 1,
                            submission: submission.answers.first { $0.questionId == question.id },
                            gradingInput: Binding(
                                get: { gradingInputs[question.id, default: 0.0] },
                                set: { gradingInputs[question.id] = $0 }
                            ),
                            feedbackInput: Binding(
                                get: { feedbackInputs[question.id, default: ""] },
                                set: { feedbackInputs[question.id] = $0 }
                            )
                        )
                    }
                    if let errorMessage = errorMessage {
                        Text(errorMessage)
                            .foregroundColor(.red)
                    }
                    if showSuccess {
                        Text("Grades saved successfully!")
                            .foregroundColor(.green)
                    }
                    HStack {
                        Button("Close") {
                            onDismiss()
                        }
                        .buttonStyle(.bordered)
                        Spacer()
                        Button(isSaving ? "Saving..." : "Save Grades") {
                            saveGrades()
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(isSaving)
                    }
                    .padding()
                }
                .padding()
            }
            .navigationTitle("Review & Grade")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            print("🎯 ManualGradingSheet onAppear: Loading grading inputs")
            for question in exercise.questions {
                if let answer = submission.answers.first(where: { $0.questionId == question.id }) {
                    if let points = answer.pointsEarned {
                        gradingInputs[question.id] = points
                    } else if question.questionType == .multipleChoice {
                        // For multiple choice, auto-fill points only if correct
                        if let answerIndex = Int(answer.answer), answerIndex == question.correctAnswerIndex {
                            gradingInputs[question.id] = Double(question.points)
                        } else {
                            gradingInputs[question.id] = 0.0
                        }
                    }
                    // For long/essay questions, leave as 0 unless manually graded
                }
            }
            print("🎯 ManualGradingSheet: Loaded gradingInputs = \(gradingInputs)")
        }
    }

    private var headerSection: some View {
        EmptyView()
    }

    private func saveGrades() {
        isSaving = true
        errorMessage = nil
        showSuccess = false
        Task {
            do {
                var grades: [QuestionGrade] = []
                var overallFeedback = "Teacher graded multiple questions."
                for question in exercise.questions {
                    let points = gradingInputs[question.id] ?? 0.0
                    let feedback = feedbackInputs[question.id] ?? ""
                    let isCorrect = points >= Double(question.points)
                    let grade = QuestionGrade(
                        questionId: question.id,
                        pointsEarned: points,
                        isCorrect: isCorrect,
                        feedback: feedback
                    )
                    grades.append(grade)
                    if !feedback.isEmpty {
                        if !overallFeedback.isEmpty && overallFeedback != "Teacher graded multiple questions." {
                            overallFeedback += " "
                        }
                        overallFeedback += "Q\(question.id.uuidString.prefix(8)): \(feedback)."
                    }
                }
                if submission.mongoId != nil {
                    try await viewModel.gradeSubmissionWithMultipleQuestions(
                        submission: submission,
                        grades: grades,
                        feedback: overallFeedback
                    )
                    print("🎯 ManualGradingSheet: Grading saved successfully, dismissing...")
                    // Close the sheet after successful save
                    onDismiss()
                } else {
                    throw NSError(domain: "SubmissionError", code: 400, userInfo: [
                        NSLocalizedDescriptionKey: "Cannot grade submission: MongoDB ObjectId is missing. Please refresh the submission data."
                    ])
                }
            } catch {
                errorMessage = error.localizedDescription
            }
            isSaving = false
        }
    }
}

struct ManualGradingQuestionCard: View {
    let question: Question
    let questionNumber: Int
    let submission: QuestionSubmission?
    @Binding var gradingInput: Double
    @Binding var feedbackInput: String

    // Helper functions for option display
    private func optionIcon(isCorrect: Bool, isStudentAnswer: Bool) -> String {
        if isCorrect && isStudentAnswer {
            return "checkmark.circle.fill"
        } else if isCorrect {
            return "checkmark.circle.fill"
        } else if isStudentAnswer {
            return "record.circle.fill"  // Radio button style for student's incorrect answer
        } else {
            return "circle"
        }
    }

    private func optionColor(isCorrect: Bool, isStudentAnswer: Bool) -> Color {
        if isCorrect {
            return .green
        } else if isStudentAnswer {
            return .red
        } else {
            return .gray
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Q\(questionNumber):")
                    .font(.headline)
                QuestionTextRenderer(text: question.questionText, fontSize: 18)
                    .fontWeight(.medium)
            }
            
            // Question content and answer
            VStack(alignment: .leading, spacing: 8) {
                if let options = question.options, question.questionType == .multipleChoice {
                    ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                        let isCorrect = index == question.correctAnswerIndex
                        let isStudentAnswer = submission?.answer == String(index)

                        HStack {
                            // Icon for the option
                            Image(systemName: optionIcon(isCorrect: isCorrect, isStudentAnswer: isStudentAnswer))
                                .foregroundColor(optionColor(isCorrect: isCorrect, isStudentAnswer: isStudentAnswer))

                            // Option text
                            QuestionTextRenderer(text: option, fontSize: 16)

                            Spacer()

                            // Label for the option
                            if isStudentAnswer && !isCorrect {
                                Text("Student's Answer")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.red)
                            } else if isCorrect && !isStudentAnswer {
                                Text("Correct Answer")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.green)
                            } else if isCorrect && isStudentAnswer {
                                Text("Correct Answer")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.green)
                            }
                        }
                    }

                    // Show message if no answer was selected
                    if submission?.answer.isEmpty != false {
                        HStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.orange)
                            Text("No answer selected")
                                .font(.subheadline)
                                .foregroundColor(.orange)
                        }
                        .padding(.top, 8)
                    }
                } else if let answer = submission?.answer, !answer.isEmpty {
                    Text("Student Answer:")
                        .font(.subheadline)
                    
                    if answer.hasPrefix("DRAWING:") {
                        DrawingAnswerView(answer: answer)
                    } else if answer.hasPrefix("PHOTO:") {
                        // Handle photo data
                        let photoData = String(answer.dropFirst(6)) // Remove "PHOTO:" prefix
                        if let imageData = Data(base64Encoded: photoData),
                           let uiImage = UIImage(data: imageData) {
                            Image(uiImage: uiImage)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 200)
                                .cornerRadius(8)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                )
                        } else {
                            Text("Invalid photo data")
                                .foregroundColor(.red)
                                .padding()
                                .background(Color.red.opacity(0.1))
                                .cornerRadius(8)
                        }
                    } else {
                        // Handle text answer
                        Text(answer)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                    }
                } else {
                    Text("No answer provided")
                        .foregroundColor(.orange)
                }
            }
            
            Divider()
            
            // Grading controls always visible
            gradingControls
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }

    private var gradingControls: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Grade (points)")
            HStack {
                Stepper(value: $gradingInput, in: 0...Double(question.points), step: 1) {
                    Text("\(Int(gradingInput)) / \(question.points)")
                }
                TextField("Enter grade", value: $gradingInput, formatter: NumberFormatter())
                    .keyboardType(.decimalPad)
                    .frame(width: 60)
                    .textFieldStyle(.roundedBorder)
            }
            Text("Feedback (optional)")
            TextField("Enter feedback", text: $feedbackInput)
                .textFieldStyle(.roundedBorder)
        }
    }
}

// MARK: - Drawing Display View

struct DrawingDisplayView: UIViewRepresentable {
    let drawing: PKDrawing
    
    func makeUIView(context: Context) -> PKCanvasView {
        let canvasView = PKCanvasView()
        canvasView.drawing = drawing
        canvasView.isUserInteractionEnabled = false // Read-only
        canvasView.backgroundColor = UIColor.white
        return canvasView
    }
    
    func updateUIView(_ uiView: PKCanvasView, context: Context) {
        uiView.drawing = drawing
    }
}

// MARK: - Drawing Answer View

struct DrawingAnswerView: View {
    let answer: String
    
    var body: some View {
        Group {
            // Handle drawing data
            let drawingData = String(answer.dropFirst(8)) // Remove "DRAWING:" prefix
            let _ = print("🎨 Drawing data length: \(drawingData.count)")
            let _ = print("🎨 Drawing data preview: \(drawingData.prefix(100))...")
            
            if let imageData = Data(base64Encoded: drawingData, options: .ignoreUnknownCharacters) {
                let _ = print("🎨 Successfully decoded base64 data, size: \(imageData.count) bytes")
                
                // Try to load as PencilKit drawing first
                if let pkDrawing = try? PKDrawing(data: imageData) {
                    let _ = print("🎨 Successfully created PKDrawing")
                    DrawingDisplayView(drawing: pkDrawing)
                        .frame(height: 300)
                        .background(Color.white)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                } else if let uiImage = UIImage(data: imageData) {
                    let _ = print("🎨 Successfully created UIImage")
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(maxHeight: 200)
                        .background(Color.white)
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                } else {
                    let _ = print("❌ Failed to create UIImage or PKDrawing from data")
                    VStack {
                        Text("Drawing data received")
                            .font(.subheadline)
                        Text("(\(imageData.count) bytes)")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text("Unable to display as image")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
            } else {
                let _ = print("❌ Failed to decode base64 data")
                Text("Invalid drawing data format")
                    .foregroundColor(.red)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
    }
}

