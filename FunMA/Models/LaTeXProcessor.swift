//
//  LaTeXProcessor.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation
import SwiftUI

// Centralized LaTeX processing with optimized parsing and validation
class LaTeXProcessor {
    
    // MARK: - Shared Instance
    static let shared = LaTeXProcessor()
    
    // MARK: - Cached Regex Patterns
    private let latexPatterns: [NSRegularExpression]
    private let mathCommandsSet: Set<String>
    private let commonWordsSet: Set<String>
    
    private init() {
        // Pre-compile regex patterns for better performance
        var patterns: [NSRegularExpression] = []
        
        let patternStrings = [
            #"\$\$[^$]+?\$\$"#,          // Display math: $$ ... $$
            #"\\?\[[^\]]+?\\?\]"#,       // Display math: \[ ... \]
            #"\\?\([^)]+?\\?\)"#,        // Inline math: \( ... \)
            #"\$[^$\n]{1,50}\$"#         // Inline math: $ ... $ (limited length)
        ]
        
        for patternString in patternStrings {
            do {
                let pattern = try NSRegularExpression(pattern: patternString, options: [.caseInsensitive])
                patterns.append(pattern)
            } catch {
                print("LaTeXProcessor: Failed to compile pattern \(patternString): \(error)")
            }
        }
        
        self.latexPatterns = patterns
        
        // Pre-populate sets for faster lookups
        self.mathCommandsSet = Set([
            "\\frac", "\\sqrt", "\\sum", "\\int", "\\lim", "\\sin", "\\cos", "\\tan",
            "\\log", "\\ln", "\\exp", "\\alpha", "\\beta", "\\gamma", "\\delta",
            "\\pi", "\\theta", "\\lambda", "\\mu", "\\sigma", "\\phi", "\\omega",
            "\\infty", "\\partial", "\\nabla", "\\cdot", "\\times", "\\div",
            "\\leq", "\\geq", "\\neq", "\\approx", "\\pm", "\\text", "\\mathbf",
            "\\mathit", "\\mathrm", "\\begin", "\\end", "\\over", "\\choose",
            "\\angle", "\\circ", "\\deg", "\\triangle", "\\parallel", "\\perp"
        ])
        
        self.commonWordsSet = Set([
            "the", "and", "or", "but", "for", "with", "has", "have", "had", "is", "are", "was", "were",
            "to", "of", "in", "on", "at", "by", "from", "up", "out", "if", "about", "into", "through",
            "during", "before", "after", "above", "below", "between", "among", "under", "over",
            "coins", "coin", "dollars", "dollar", "money", "price", "cost", "total", "amount",
            "therefore", "there", "here", "this", "that", "these", "those", "what", "where", "when",
            "why", "how", "who", "which", "will", "would", "could", "should"
        ])
    }
    
    // MARK: - Main Processing Methods
    
    /// Parse content into segments of text and LaTeX
    func parseContent(_ content: String) -> [ContentSegment] {
        let text = content.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return [] }
        
        // First, try to auto-fix obvious LaTeX patterns that are missing delimiters
        let preprocessedText = autoFixMissingDelimiters(text)
        
        var allMatches: [(range: Range<String.Index>, text: String, type: LaTeXType)] = []
        
        // Use compiled patterns for better performance
        for (index, pattern) in latexPatterns.enumerated() {
            let range = NSRange(preprocessedText.startIndex..<preprocessedText.endIndex, in: preprocessedText)
            let matches = pattern.matches(in: preprocessedText, options: [], range: range)
            
            for match in matches {
                if let matchRange = Range(match.range, in: preprocessedText) {
                    let matchedText = String(preprocessedText[matchRange])
                    let type = LaTeXType.from(index: index)
                    
                    if isValidLaTeX(matchedText, type: type) {
                        allMatches.append((range: matchRange, text: matchedText, type: type))
                    }
                }
            }
        }
        
        // Sort and remove overlaps
        allMatches.sort { $0.range.lowerBound < $1.range.lowerBound }
        allMatches = removeOverlappingMatches(allMatches)
        
        return buildSegments(from: preprocessedText, matches: allMatches)
    }
    
    /// Auto-detect and fix common LaTeX patterns that are missing delimiters
    private func autoFixMissingDelimiters(_ text: String) -> String {
        var result = text
        
        // Auto-detection patterns for common LaTeX expressions without delimiters
        let latexPatterns: [(String, String)] = [
            // Geometric expressions with angle symbols - keep as display math for complex expressions
            (#"(?<!\$)(∠[A-Z]{2,3}\s*=\s*\([^)]+\)°)\b(?!\$)"#, "$$$1$$"),
            (#"(?<!\$)(∠[A-Z]{2,3})\b(?!\$)"#, "$$$1$$"),
            
            // Mathematical expressions with degree symbols - use inline math for better flow
            (#"(?<!\$)(\([^)]+\)°)\b(?!\$)"#, "$$1$"),
            (#"(?<!\$)(\d+[xy]?\s*[+\-]\s*\d+)°\b(?!\$)"#, "$($1)°$"),
            (#"(?<!\$)(\d+[xy]?)°\b(?!\$)"#, "$$1°$"),
            
            // Mathematical expressions with LaTeX commands that need wrapping
            (#"(?<!\$)\\times(?!\$)"#, "$\\times$"),
            (#"(?<!\$)\\cdot(?!\$)"#, "$\\cdot$"),
            (#"(?<!\$)\\div(?!\$)"#, "$\\div$"),
            (#"(?<!\$)\\pm(?!\$)"#, "$\\pm$"),
            (#"(?<!\$)\\mp(?!\$)"#, "$\\mp$"),
            (#"(?<!\$)\\circ(?!\$)"#, "$\\circ$"),
            (#"(?<!\$)\\deg(?!\$)"#, "$\\deg$"),
            (#"(?<!\$)\\angle(?!\$)"#, "$\\angle$"),
            (#"(?<!\$)\\triangle(?!\$)"#, "$\\triangle$"),
            
            // Greek letters that appear standalone
            (#"(?<!\$)\\alpha(?!\$)"#, "$\\alpha$"),
            (#"(?<!\$)\\beta(?!\$)"#, "$\\beta$"),
            (#"(?<!\$)\\gamma(?!\$)"#, "$\\gamma$"),
            (#"(?<!\$)\\delta(?!\$)"#, "$\\delta$"),
            (#"(?<!\$)\\theta(?!\$)"#, "$\\theta$"),
            (#"(?<!\$)\\lambda(?!\$)"#, "$\\lambda$"),
            (#"(?<!\$)\\mu(?!\$)"#, "$\\mu$"),
            (#"(?<!\$)\\pi(?!\$)"#, "$\\pi$"),
            (#"(?<!\$)\\sigma(?!\$)"#, "$\\sigma$"),
            (#"(?<!\$)\\phi(?!\$)"#, "$\\phi$"),
            (#"(?<!\$)\\omega(?!\$)"#, "$\\omega$"),
            
            // Math operators with fractions
            (#"(?<!\$)\b(\d+/\d+)\b(?!\$)"#, "$$$1$$"),
            
            // Simple equations - be more selective
            (#"(?<!\$)\b([a-zA-Z]\s*=\s*\d+(?:\.\d+)?)\b(?!\$)"#, "$$$1$$"),
            (#"(?<!\$)\b(\d+(?:\.\d+)?\s*=\s*[a-zA-Z])\b(?!\$)"#, "$$$1$$"),
            
            // Mathematical expressions with operators - more selective patterns
            (#"(?<!\$)\b([a-zA-Z]\s*[+\-×÷]\s*\d+)\b(?!\$)"#, "$$$1$$"),
            (#"(?<!\$)\b(\d+\s*[+\-×÷]\s*[a-zA-Z])\b(?!\$)"#, "$$$1$$"),
            
            // Superscripts and subscripts - only if clearly mathematical
            (#"(?<!\$)\b([a-zA-Z]\^[a-zA-Z0-9])\b(?!\$)"#, "$$$1$$"),
            (#"(?<!\$)\b([a-zA-Z]_[a-zA-Z0-9])\b(?!\$)"#, "$$$1$$"),
            
            // Square roots and functions - only standard math functions
            (#"(?<!\$)\b(sqrt\([^)]+\))\b(?!\$)"#, "$$$1$$"),
            (#"(?<!\$)\b((sin|cos|tan|log|ln)\([^)]+\))\b(?!\$)"#, "$$$1$$"),
        ]
        
        for (pattern, replacement) in latexPatterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
                result = regex.stringByReplacingMatches(in: result,
                                                       options: [],
                                                       range: NSRange(result.startIndex..<result.endIndex, in: result),
                                                       withTemplate: replacement)
            } catch {
                print("AutoFix regex error for pattern \(pattern): \(error)")
            }
        }
        
        // Post-process to fix common issues from auto-detection
        // Fix multiple consecutive dollar signs
        result = result.replacingOccurrences(of: "$$$$", with: "$$") // Remove quadruple delimiters
        result = result.replacingOccurrences(of: "$$$", with: "$")   // Remove triple delimiters
        
        // Fix cases where patterns created malformed delimiters
        result = result.replacingOccurrences(of: "$$$", with: "$")   // Still needed after first pass
        result = result.replacingOccurrences(of: "$$$$", with: "$$") // Still needed after first pass
        
        return result
    }
    
    /// Clean LaTeX expression by removing delimiters and fixing common issues
    func cleanLaTeX(_ expression: String) -> String {
        var cleaned = expression.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove delimiters - handle nested dollar signs carefully
        if cleaned.hasPrefix("$$") && cleaned.hasSuffix("$$") && cleaned.count > 4 {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        } else if cleaned.hasPrefix("$") && cleaned.hasSuffix("$") && cleaned.count > 2 {
            cleaned = String(cleaned.dropFirst().dropLast())
        } else if cleaned.hasPrefix("\\[") && cleaned.hasSuffix("\\]") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        } else if cleaned.hasPrefix("\\(") && cleaned.hasSuffix("\\)") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        }
        
        // Normalize common mathematical notations for better rendering
        let mathNormalizations = [
            // Degree symbols - ensure proper LaTeX format
            ("^\\\\circ", "^\\circ"),     // Fix over-escaped degree symbols
            ("^\\\\deg", "^\\deg"),       // Fix over-escaped degree abbreviations
            ("\\\\circ", "\\circ"),       // Fix over-escaped degree symbols
            ("\\\\deg", "\\deg"),         // Fix over-escaped degree abbreviations
            
            // Common math functions - normalize escaping
            ("\\\\frac", "\\frac"),
            ("\\\\sqrt", "\\sqrt"),
            ("\\\\text", "\\text"),
            ("\\\\sin", "\\sin"),
            ("\\\\cos", "\\cos"),
            ("\\\\tan", "\\tan"),
            ("\\\\log", "\\log"),
            ("\\\\ln", "\\ln"),
            
            // Common math operators - normalize escaping
            ("\\\\times", "\\times"),
            ("\\\\cdot", "\\cdot"),
            ("\\\\div", "\\div"),
            ("\\\\pm", "\\pm"),
            ("\\\\mp", "\\mp"),
            
            // Greek letters - normalize escaping
            ("\\\\alpha", "\\alpha"),
            ("\\\\beta", "\\beta"),
            ("\\\\gamma", "\\gamma"),
            ("\\\\delta", "\\delta"),
            ("\\\\theta", "\\theta"),
            ("\\\\lambda", "\\lambda"),
            ("\\\\mu", "\\mu"),
            ("\\\\pi", "\\pi"),
            ("\\\\sigma", "\\sigma"),
            ("\\\\phi", "\\phi"),
            ("\\\\omega", "\\omega"),
            
            // Comparison operators
            ("\\\\leq", "\\leq"),
            ("\\leq", "≤"),
            ("\\\\le", "≤"),
            ("\\le", "≤"),
            ("\\\\geq", "≥"),
            ("\\geq", "≥"),
            ("\\\\ge", "≥"),
            ("\\ge", "≥"),
            ("\\\\ll", "≪"),
            ("\\ll", "≪"),
            ("\\\\gg", "≫"),
            ("\\gg", "≫"),
            ("\\\\approx", "≈"),
            ("\\approx", "≈"),
            ("\\\\equiv", "≡"),
            ("\\equiv", "≡"),
            ("\\\\sim", "∼"),
            ("\\sim", "∼"),
            ("\\\\simeq", "≃"),
            ("\\simeq", "≃"),
            ("\\\\cong", "≅"),
            ("\\cong", "≅"),
            ("\\\\propto", "∝"),
            ("\\propto", "∝"),
        ]
        
        for (pattern, replacement) in mathNormalizations {
            cleaned = cleaned.replacingOccurrences(of: pattern, with: replacement)
        }
        
        // Remove any remaining internal dollar signs that shouldn't be there
        // But be more careful to preserve valid LaTeX commands
        let internalDollarPattern = #"\$+([^$]*)\$+"#
        do {
            let regex = try NSRegularExpression(pattern: internalDollarPattern, options: [])
            let range = NSRange(cleaned.startIndex..<cleaned.endIndex, in: cleaned)
            let matches = regex.matches(in: cleaned, options: [], range: range)
            
            // Only remove dollars if the content doesn't contain LaTeX commands
            for match in matches.reversed() {
                if let matchRange = Range(match.range, in: cleaned),
                   let captureRange = Range(match.range(at: 1), in: cleaned) {
                    let capturedContent = String(cleaned[captureRange])
                    // Only strip dollars if it doesn't contain LaTeX commands
                    if !capturedContent.contains("\\") && !capturedContent.contains("^") && !capturedContent.contains("_") {
                        cleaned.replaceSubrange(matchRange, with: capturedContent)
                    }
                }
            }
        } catch {
            print("Error removing internal dollars: \(error)")
        }
        
        // Fix common malformed patterns - but preserve valid math notation
        let malformedPatterns = [
            ("$}$", ""),
            ("$$}$$", ""),
            ("$$}}", ""),
            ("$}}", ""),
            ("$$$", "$"),
            ("$$$$", "$$")
        ]
        
        for (pattern, replacement) in malformedPatterns {
            cleaned = cleaned.replacingOccurrences(of: pattern, with: replacement)
        }
        
        // Remove unbalanced closing braces (but be careful not to break valid LaTeX)
        cleaned = removeUnbalancedBraces(cleaned)
        
        return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // MARK: - Comprehensive LaTeX to Unicode Conversion
    
    /// Comprehensive function to convert LaTeX expressions to Unicode symbols for better display
    func convertLatexToUnicode(_ expression: String) -> String {
        var converted = expression.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove LaTeX delimiters first
        converted = removeLaTeXDelimiters(converted)
        
        // Apply comprehensive LaTeX-to-Unicode conversions
        converted = applyBasicSymbolConversions(converted)
        converted = applyGreekLetterConversions(converted)
        converted = applyMathOperatorConversions(converted)
        converted = applyFunctionConversions(converted)
        converted = applyFractionConversions(converted)
        converted = applySuperscriptSubscriptConversions(converted)
        converted = applySpecialSymbolConversions(converted)
        converted = applyGeometrySymbolConversions(converted)
        converted = applyArrowSymbolConversions(converted)
        converted = applySetTheorySymbolConversions(converted)
        converted = applyLogicSymbolConversions(converted)
        
        // Clean up remaining LaTeX artifacts
        converted = cleanupLatexArtifacts(converted)
        
        return converted.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// Remove LaTeX delimiters ($, $$, \[, \], \(, \))
    private func removeLaTeXDelimiters(_ text: String) -> String {
        var cleaned = text
        
        // Remove display math delimiters
        if cleaned.hasPrefix("$$") && cleaned.hasSuffix("$$") && cleaned.count > 4 {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        }
        // Remove inline math delimiters
        else if cleaned.hasPrefix("$") && cleaned.hasSuffix("$") && cleaned.count > 2 {
            cleaned = String(cleaned.dropFirst().dropLast())
        }
        // Remove bracket delimiters
        else if cleaned.hasPrefix("\\[") && cleaned.hasSuffix("\\]") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        }
        // Remove parenthesis delimiters
        else if cleaned.hasPrefix("\\(") && cleaned.hasSuffix("\\)") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        }
        
        return cleaned
    }
    
    /// Convert basic mathematical symbols
    private func applyBasicSymbolConversions(_ text: String) -> String {
        let conversions = [
            // Degree symbols (handle all variations)
            ("\\^\\\\circ", "°"),
            ("\\^\\{\\\\circ\\}", "°"),
            ("\\^\\circ", "°"),
            ("\\^\\{\\circ\\}", "°"),
            ("\\\\circ", "°"),
            ("\\circ", "°"),
            ("\\\\deg", "°"),
            ("\\deg", "°"),
            ("\\\\degree", "°"),
            ("\\degree", "°"),
            
            // Basic arithmetic
            ("\\\\times", "×"),
            ("\\times", "×"),
            ("\\\\div", "÷"),
            ("\\div", "÷"),
            ("\\\\cdot", "·"),
            ("\\cdot", "·"),
            ("\\\\pm", "±"),
            ("\\pm", "±"),
            ("\\\\mp", "∓"),
            ("\\mp", "∓"),
            
            // Equality and comparison
            ("\\\\neq", "≠"),
            ("\\neq", "≠"),
            ("\\\\ne", "≠"),
            ("\\ne", "≠"),
            ("\\\\leq", "≤"),
            ("\\leq", "≤"),
            ("\\\\le", "≤"),
            ("\\le", "≤"),
            ("\\\\geq", "≥"),
            ("\\geq", "≥"),
            ("\\\\ge", "≥"),
            ("\\ge", "≥"),
            ("\\\\ll", "≪"),
            ("\\ll", "≪"),
            ("\\\\gg", "≫"),
            ("\\gg", "≫"),
            ("\\\\approx", "≈"),
            ("\\approx", "≈"),
            ("\\\\equiv", "≡"),
            ("\\equiv", "≡"),
            ("\\\\sim", "∼"),
            ("\\sim", "∼"),
            ("\\\\simeq", "≃"),
            ("\\simeq", "≃"),
            ("\\\\cong", "≅"),
            ("\\cong", "≅"),
            ("\\\\propto", "∝"),
            ("\\propto", "∝"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert Greek letters
    private func applyGreekLetterConversions(_ text: String) -> String {
        let conversions = [
            // Lowercase Greek letters
            ("\\\\alpha", "α"),
            ("\\alpha", "α"),
            ("\\\\beta", "β"),
            ("\\beta", "β"),
            ("\\\\gamma", "γ"),
            ("\\gamma", "γ"),
            ("\\\\delta", "δ"),
            ("\\delta", "δ"),
            ("\\\\epsilon", "ε"),
            ("\\epsilon", "ε"),
            ("\\\\varepsilon", "ε"),
            ("\\varepsilon", "ε"),
            ("\\\\zeta", "ζ"),
            ("\\zeta", "ζ"),
            ("\\\\eta", "η"),
            ("\\eta", "η"),
            ("\\\\theta", "θ"),
            ("\\theta", "θ"),
            ("\\\\vartheta", "ϑ"),
            ("\\vartheta", "ϑ"),
            ("\\\\iota", "ι"),
            ("\\iota", "ι"),
            ("\\\\kappa", "κ"),
            ("\\kappa", "κ"),
            ("\\\\lambda", "λ"),
            ("\\lambda", "λ"),
            ("\\\\mu", "μ"),
            ("\\mu", "μ"),
            ("\\\\nu", "ν"),
            ("\\nu", "ν"),
            ("\\\\xi", "ξ"),
            ("\\xi", "ξ"),
            ("\\\\omicron", "ο"),
            ("\\omicron", "ο"),
            ("\\\\pi", "π"),
            ("\\pi", "π"),
            ("\\\\varpi", "ϖ"),
            ("\\varpi", "ϖ"),
            ("\\\\rho", "ρ"),
            ("\\rho", "ρ"),
            ("\\\\varrho", "ϱ"),
            ("\\varrho", "ϱ"),
            ("\\\\sigma", "σ"),
            ("\\sigma", "σ"),
            ("\\\\varsigma", "ς"),
            ("\\varsigma", "ς"),
            ("\\\\tau", "τ"),
            ("\\tau", "τ"),
            ("\\\\upsilon", "υ"),
            ("\\upsilon", "υ"),
            ("\\\\phi", "φ"),
            ("\\phi", "φ"),
            ("\\\\varphi", "ϕ"),
            ("\\varphi", "ϕ"),
            ("\\\\chi", "χ"),
            ("\\chi", "χ"),
            ("\\\\psi", "ψ"),
            ("\\psi", "ψ"),
            ("\\\\omega", "ω"),
            ("\\omega", "ω"),
            
            // Uppercase Greek letters
            ("\\\\Alpha", "Α"),
            ("\\Alpha", "Α"),
            ("\\\\Beta", "Β"),
            ("\\Beta", "Β"),
            ("\\\\Gamma", "Γ"),
            ("\\Gamma", "Γ"),
            ("\\\\Delta", "Δ"),
            ("\\Delta", "Δ"),
            ("\\\\Epsilon", "Ε"),
            ("\\Epsilon", "Ε"),
            ("\\\\Zeta", "Ζ"),
            ("\\Zeta", "Ζ"),
            ("\\\\Eta", "Η"),
            ("\\Eta", "Η"),
            ("\\\\Theta", "Θ"),
            ("\\Theta", "Θ"),
            ("\\\\Iota", "Ι"),
            ("\\Iota", "Ι"),
            ("\\\\Kappa", "Κ"),
            ("\\Kappa", "Κ"),
            ("\\\\Lambda", "Λ"),
            ("\\Lambda", "Λ"),
            ("\\\\Mu", "Μ"),
            ("\\Mu", "Μ"),
            ("\\\\Nu", "Ν"),
            ("\\Nu", "Ν"),
            ("\\\\Xi", "Ξ"),
            ("\\Xi", "Ξ"),
            ("\\\\Omicron", "Ο"),
            ("\\Omicron", "Ο"),
            ("\\\\Pi", "Π"),
            ("\\Pi", "Π"),
            ("\\\\Rho", "Ρ"),
            ("\\Rho", "Ρ"),
            ("\\\\Sigma", "Σ"),
            ("\\Sigma", "Σ"),
            ("\\\\Tau", "Τ"),
            ("\\Tau", "Τ"),
            ("\\\\Upsilon", "Υ"),
            ("\\Upsilon", "Υ"),
            ("\\\\Phi", "Φ"),
            ("\\Phi", "Φ"),
            ("\\\\Chi", "Χ"),
            ("\\Chi", "Χ"),
            ("\\\\Psi", "Ψ"),
            ("\\Psi", "Ψ"),
            ("\\\\Omega", "Ω"),
            ("\\Omega", "Ω"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert mathematical operators and symbols
    private func applyMathOperatorConversions(_ text: String) -> String {
        let conversions = [
            // Calculus and analysis
            ("\\\\partial", "∂"),
            ("\\partial", "∂"),
            ("\\\\nabla", "∇"),
            ("\\nabla", "∇"),
            ("\\\\infty", "∞"),
            ("\\infty", "∞"),
            ("\\\\int", "∫"),
            ("\\int", "∫"),
            ("\\\\iint", "∬"),
            ("\\iint", "∬"),
            ("\\\\iiint", "∭"),
            ("\\iiint", "∭"),
            ("\\\\oint", "∮"),
            ("\\oint", "∮"),
            ("\\\\sum", "∑"),
            ("\\sum", "∑"),
            ("\\\\prod", "∏"),
            ("\\prod", "∏"),
            ("\\\\coprod", "∐"),
            ("\\coprod", "∐"),
            
            // Set theory and logic
            ("\\\\in", "∈"),
            ("\\in", "∈"),
            ("\\\\notin", "∉"),
            ("\\notin", "∉"),
            ("\\\\ni", "∋"),
            ("\\ni", "∋"),
            ("\\\\subset", "⊂"),
            ("\\subset", "⊂"),
            ("\\\\supset", "⊃"),
            ("\\supset", "⊃"),
            ("\\\\subseteq", "⊆"),
            ("\\subseteq", "⊆"),
            ("\\\\supseteq", "⊇"),
            ("\\supseteq", "⊇"),
            ("\\\\cup", "∪"),
            ("\\cup", "∪"),
            ("\\\\cap", "∩"),
            ("\\cap", "∩"),
            ("\\\\emptyset", "∅"),
            ("\\emptyset", "∅"),
            ("\\\\varnothing", "∅"),
            ("\\varnothing", "∅"),
            ("\\\\forall", "∀"),
            ("\\forall", "∀"),
            ("\\\\exists", "∃"),
            ("\\exists", "∃"),
            ("\\\\nexists", "∄"),
            ("\\nexists", "∄"),
            
            // Logic operators
            ("\\\\land", "∧"),
            ("\\land", "∧"),
            ("\\\\lor", "∨"),
            ("\\lor", "∨"),
            ("\\\\neg", "¬"),
            ("\\neg", "¬"),
            ("\\\\lnot", "¬"),
            ("\\lnot", "¬"),
            ("\\\\implies", "⟹"),
            ("\\implies", "⟹"),
            ("\\\\iff", "⟺"),
            ("\\iff", "⟺"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert mathematical functions
    private func applyFunctionConversions(_ text: String) -> String {
        let conversions = [
            // Trigonometric functions
            ("\\\\sin", "sin"),
            ("\\sin", "sin"),
            ("\\\\cos", "cos"),
            ("\\cos", "cos"),
            ("\\\\tan", "tan"),
            ("\\tan", "tan"),
            ("\\\\cot", "cot"),
            ("\\cot", "cot"),
            ("\\\\sec", "sec"),
            ("\\sec", "sec"),
            ("\\\\csc", "csc"),
            ("\\csc", "csc"),
            
            // Inverse trigonometric functions
            ("\\\\arcsin", "arcsin"),
            ("\\arcsin", "arcsin"),
            ("\\\\arccos", "arccos"),
            ("\\arccos", "arccos"),
            ("\\\\arctan", "arctan"),
            ("\\arctan", "arctan"),
            
            // Hyperbolic functions
            ("\\\\sinh", "sinh"),
            ("\\sinh", "sinh"),
            ("\\\\cosh", "cosh"),
            ("\\cosh", "cosh"),
            ("\\\\tanh", "tanh"),
            ("\\tanh", "tanh"),
            
            // Logarithmic functions
            ("\\\\log", "log"),
            ("\\log", "log"),
            ("\\\\ln", "ln"),
            ("\\ln", "ln"),
            ("\\\\lg", "lg"),
            ("\\lg", "lg"),
            ("\\\\exp", "exp"),
            ("\\exp", "exp"),
            
            // Other functions
            ("\\\\max", "max"),
            ("\\max", "max"),
            ("\\\\min", "min"),
            ("\\min", "min"),
            ("\\\\sup", "sup"),
            ("\\sup", "sup"),
            ("\\\\inf", "inf"),
            ("\\inf", "inf"),
            ("\\\\lim", "lim"),
            ("\\lim", "lim"),
            ("\\\\det", "det"),
            ("\\det", "det"),
            ("\\\\dim", "dim"),
            ("\\dim", "dim"),
            ("\\\\gcd", "gcd"),
            ("\\gcd", "gcd"),
            ("\\\\lcm", "lcm"),
            ("\\lcm", "lcm"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert fractions to a more readable format
    private func applyFractionConversions(_ text: String) -> String {
        var converted = text
        
        // Handle \frac{numerator}{denominator} patterns
        let fractionPattern = #"\\\\?frac\{([^}]+)\}\{([^}]+)\}"#
        do {
            let regex = try NSRegularExpression(pattern: fractionPattern, options: [])
            let matches = regex.matches(in: converted, options: [], range: NSRange(converted.startIndex..., in: converted))
            
            // Process matches in reverse order to maintain string indices
            for match in matches.reversed() {
                if let matchRange = Range(match.range, in: converted),
                   let numeratorRange = Range(match.range(at: 1), in: converted),
                   let denominatorRange = Range(match.range(at: 2), in: converted) {
                    
                    let numerator = String(converted[numeratorRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                    let denominator = String(converted[denominatorRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                    
                    // Create a readable fraction format
                    let fraction = "(\(numerator)/\(denominator))"
                    converted.replaceSubrange(matchRange, with: fraction)
                }
            }
        } catch {
            print("Error processing fractions: \(error)")
        }
        
        // Handle simple fractions like \over
        converted = converted.replacingOccurrences(of: "\\\\over", with: "/")
        converted = converted.replacingOccurrences(of: "\\over", with: "/")
        
        return converted
    }
    
    /// Convert superscripts and subscripts to Unicode where possible
    private func applySuperscriptSubscriptConversions(_ text: String) -> String {
        var converted = text
        
        // Unicode superscript mappings
        let superscriptMap: [String: String] = [
            "0": "⁰", "1": "¹", "2": "²", "3": "³", "4": "⁴",
            "5": "⁵", "6": "⁶", "7": "⁷", "8": "⁸", "9": "⁹",
            "+": "⁺", "-": "⁻", "=": "⁼", "(": "⁽", ")": "⁾",
            "n": "ⁿ", "i": "ⁱ"
        ]
        
        // Unicode subscript mappings
        let subscriptMap: [String: String] = [
            "0": "₀", "1": "₁", "2": "₂", "3": "₃", "4": "₄",
            "5": "₅", "6": "₆", "7": "₇", "8": "₈", "9": "₉",
            "+": "₊", "-": "₋", "=": "₌", "(": "₍", ")": "₎",
            "a": "ₐ", "e": "ₑ", "h": "ₕ", "i": "ᵢ", "j": "ⱼ",
            "k": "ₖ", "l": "ₗ", "m": "ₘ", "n": "ₙ", "o": "ₒ",
            "p": "ₚ", "r": "ᵣ", "s": "ₛ", "t": "ₜ", "u": "ᵤ",
            "v": "ᵥ", "x": "ₓ"
        ]
        
        // Convert simple superscripts ^{single_char} or ^single_char
        for (char, superscript) in superscriptMap {
            converted = converted.replacingOccurrences(of: "^{\(char)}", with: superscript)
            converted = converted.replacingOccurrences(of: "^\(char)", with: superscript)
        }
        
        // Convert simple subscripts _{single_char} or _single_char
        for (char, subscriptChar) in subscriptMap {
            converted = converted.replacingOccurrences(of: "_{\(char)}", with: subscriptChar)
            converted = converted.replacingOccurrences(of: "_\(char)", with: subscriptChar)
        }
        
        // For complex superscripts/subscripts, use parentheses notation
        let superscriptPattern = #"\^{([^}]+)}"#
        let subscriptPattern = #"_{([^}]+)}"#
        
        do {
            // Handle complex superscripts
            let superRegex = try NSRegularExpression(pattern: superscriptPattern, options: [])
            converted = superRegex.stringByReplacingMatches(
                in: converted,
                options: [],
                range: NSRange(converted.startIndex..., in: converted),
                withTemplate: "^($1)"
            )
            
            // Handle complex subscripts
            let subRegex = try NSRegularExpression(pattern: subscriptPattern, options: [])
            converted = subRegex.stringByReplacingMatches(
                in: converted,
                options: [],
                range: NSRange(converted.startIndex..., in: converted),
                withTemplate: "_($1)"
            )
        } catch {
            print("Error processing superscripts/subscripts: \(error)")
        }
        
        return converted
    }
    
    /// Convert special mathematical symbols
    private func applySpecialSymbolConversions(_ text: String) -> String {
        let conversions = [
            // Roots
            ("\\\\sqrt", "√"),
            ("\\sqrt", "√"),
            ("\\\\sqrt\\[3\\]", "∛"),
            ("\\sqrt\\[3\\]", "∛"),
            ("\\\\sqrt\\[4\\]", "∜"),
            ("\\sqrt\\[4\\]", "∜"),
            
            // Miscellaneous symbols
            ("\\\\hbar", "ℏ"),
            ("\\hbar", "ℏ"),
            ("\\\\ell", "ℓ"),
            ("\\ell", "ℓ"),
            ("\\\\wp", "℘"),
            ("\\wp", "℘"),
            ("\\\\Re", "ℜ"),
            ("\\Re", "ℜ"),
            ("\\\\Im", "ℑ"),
            ("\\Im", "ℑ"),
            ("\\\\aleph", "ℵ"),
            ("\\aleph", "ℵ"),
            ("\\\\beth", "ℶ"),
            ("\\beth", "ℶ"),
            ("\\\\gimel", "ℷ"),
            ("\\gimel", "ℷ"),
            ("\\\\daleth", "ℸ"),
            ("\\daleth", "ℸ"),
            
            // Currency and number symbols
            ("\\\\pounds", "£"),
            ("\\pounds", "£"),
            ("\\\\#", "#"),
            ("\\#", "#"),
            ("\\\\%", "%"),
            ("\\%", "%"),
            ("\\\\&", "&"),
            ("\\&", "&"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert geometry-related symbols
    private func applyGeometrySymbolConversions(_ text: String) -> String {
        let conversions = [
            // Angles and geometric shapes
            ("\\\\angle", "∠"),
            ("\\angle", "∠"),
            ("\\\\measuredangle", "∡"),
            ("\\measuredangle", "∡"),
            ("\\\\sphericalangle", "∢"),
            ("\\sphericalangle", "∢"),
            ("\\\\triangle", "△"),
            ("\\triangle", "△"),
            ("\\\\triangledown", "▽"),
            ("\\triangledown", "▽"),
            ("\\\\square", "□"),
            ("\\square", "□"),
            ("\\\\blacksquare", "■"),
            ("\\blacksquare", "■"),
            ("\\\\diamond", "◊"),
            ("\\diamond", "◊"),
            ("\\\\blackdiamond", "♦"),
            ("\\blackdiamond", "♦"),
            ("\\\\pentagon", "⬟"),
            ("\\pentagon", "⬟"),
            ("\\\\hexagon", "⬡"),
            ("\\hexagon", "⬡"),
            ("\\\\star", "⋆"),
            ("\\star", "⋆"),
            ("\\\\bigstar", "★"),
            ("\\bigstar", "★"),
            
            // Parallel and perpendicular
            ("\\\\parallel", "∥"),
            ("\\parallel", "∥"),
            ("\\\\nparallel", "∦"),
            ("\\nparallel", "∦"),
            ("\\\\perp", "⊥"),
            ("\\perp", "⊥"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert arrow symbols
    private func applyArrowSymbolConversions(_ text: String) -> String {
        let conversions = [
            // Basic arrows
            ("\\\\leftarrow", "←"),
            ("\\leftarrow", "←"),
            ("\\\\gets", "←"),
            ("\\gets", "←"),
            ("\\\\rightarrow", "→"),
            ("\\rightarrow", "→"),
            ("\\\\to", "→"),
            ("\\to", "→"),
            ("\\\\leftrightarrow", "↔"),
            ("\\leftrightarrow", "↔"),
            ("\\\\uparrow", "↑"),
            ("\\uparrow", "↑"),
            ("\\\\downarrow", "↓"),
            ("\\downarrow", "↓"),
            ("\\\\updownarrow", "↕"),
            ("\\updownarrow", "↕"),
            
            // Double arrows
            ("\\\\Leftarrow", "⇐"),
            ("\\Leftarrow", "⇐"),
            ("\\\\Rightarrow", "⇒"),
            ("\\Rightarrow", "⇒"),
            ("\\\\Leftrightarrow", "⇔"),
            ("\\Leftrightarrow", "⇔"),
            ("\\\\Uparrow", "⇑"),
            ("\\Uparrow", "⇑"),
            ("\\\\Downarrow", "⇓"),
            ("\\Downarrow", "⇓"),
            ("\\\\Updownarrow", "⇕"),
            ("\\Updownarrow", "⇕"),
            
            // Long arrows
            ("\\\\longleftarrow", "⟵"),
            ("\\longleftarrow", "⟵"),
            ("\\\\longrightarrow", "⟶"),
            ("\\longrightarrow", "⟶"),
            ("\\\\longleftrightarrow", "⟷"),
            ("\\longleftrightarrow", "⟷"),
            ("\\\\Longleftarrow", "⟸"),
            ("\\Longleftarrow", "⟸"),
            ("\\\\Longrightarrow", "⟹"),
            ("\\Longrightarrow", "⟹"),
            ("\\\\Longleftrightarrow", "⟺"),
            ("\\Longleftrightarrow", "⟺"),
            
            // Curved arrows
            ("\\\\hookleftarrow", "↩"),
            ("\\hookleftarrow", "↩"),
            ("\\\\hookrightarrow", "↪"),
            ("\\hookrightarrow", "↪"),
            ("\\\\looparrowleft", "↫"),
            ("\\looparrowleft", "↫"),
            ("\\\\looparrowright", "↬"),
            ("\\looparrowright", "↬"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert set theory symbols
    private func applySetTheorySymbolConversions(_ text: String) -> String {
        let conversions = [
            // Set operations
            ("\\\\setminus", "∖"),
            ("\\setminus", "∖"),
            ("\\\\smallsetminus", "∖"),
            ("\\smallsetminus", "∖"),
            ("\\\\bigcup", "⋃"),
            ("\\bigcup", "⋃"),
            ("\\\\bigcap", "⋂"),
            ("\\bigcap", "⋂"),
            ("\\\\sqcup", "⊔"),
            ("\\sqcup", "⊔"),
            ("\\\\sqcap", "⊓"),
            ("\\sqcap", "⊓"),
            ("\\\\bigsqcup", "⨆"),
            ("\\bigsqcup", "⨆"),
            
            // Subset/superset variations
            ("\\\\nsubset", "⊄"),
            ("\\nsubset", "⊄"),
            ("\\\\nsupset", "⊅"),
            ("\\nsupset", "⊅"),
            ("\\\\nsubseteq", "⊈"),
            ("\\nsubseteq", "⊈"),
            ("\\\\nsupseteq", "⊉"),
            ("\\nsupseteq", "⊉"),
            ("\\\\subsetneq", "⊊"),
            ("\\subsetneq", "⊊"),
            ("\\\\supsetneq", "⊋"),
            ("\\supsetneq", "⊋"),
            
            // Power set and cardinality
            ("\\\\mathcal\\{P\\}", "℘"),
            ("\\mathcal\\{P\\}", "℘"),
            ("\\\\|", "|"),  // For cardinality notation
            ("|", "|"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Convert logic symbols
    private func applyLogicSymbolConversions(_ text: String) -> String {
        let conversions = [
            // Boolean algebra
            ("\\\\wedge", "∧"),
            ("\\wedge", "∧"),
            ("\\\\vee", "∨"),
            ("\\vee", "∨"),
            ("\\\\bigwedge", "⋀"),
            ("\\bigwedge", "⋀"),
            ("\\\\bigvee", "⋁"),
            ("\\bigvee", "⋁"),
            
            // Quantifiers
            ("\\\\therefore", "∴"),
            ("\\therefore", "∴"),
            ("\\\\because", "∵"),
            ("\\because", "∵"),
            ("\\\\QED", "∎"),
            ("\\QED", "∎"),
            ("\\\\blacksquare", "∎"),  // End of proof
            ("\\blacksquare", "∎"),
            
            // Model theory
            ("\\\\models", "⊨"),
            ("\\models", "⊨"),
            ("\\\\vdash", "⊢"),
            ("\\vdash", "⊢"),
            ("\\\\dashv", "⊣"),
            ("\\dashv", "⊣"),
            ("\\\\vDash", "⊨"),
            ("\\vDash", "⊨"),
            ("\\\\Vdash", "⊩"),
            ("\\Vdash", "⊩"),
            ("\\\\Vvdash", "⊪"),
            ("\\Vvdash", "⊪"),
        ]
        
        return applyConversions(text, conversions)
    }
    
    /// Apply a list of conversions to text
    private func applyConversions(_ text: String, _ conversions: [(String, String)]) -> String {
        var result = text
        
        for (pattern, replacement) in conversions {
            // Use string replacement for simple patterns
            if !pattern.contains("\\") || pattern.hasPrefix("\\\\") {
                result = result.replacingOccurrences(of: pattern, with: replacement)
            } else {
                // Use regex for complex patterns
                do {
                    let regex = try NSRegularExpression(pattern: pattern, options: [])
                    result = regex.stringByReplacingMatches(
                        in: result,
                        options: [],
                        range: NSRange(result.startIndex..., in: result),
                        withTemplate: replacement
                    )
                } catch {
                    // Fall back to string replacement if regex fails
                    result = result.replacingOccurrences(of: pattern, with: replacement)
                }
            }
        }
        
        return result
    }
    
    /// Clean up remaining LaTeX artifacts after conversion
    private func cleanupLatexArtifacts(_ text: String) -> String {
        var cleaned = text
        
        // Remove common LaTeX commands that don't have Unicode equivalents
        let commandsToRemove = [
            "\\\\left", "\\left",
            "\\\\right", "\\right",
            "\\\\big", "\\big",
            "\\\\Big", "\\Big",
            "\\\\bigg", "\\bigg",
            "\\\\Bigg", "\\Bigg",
            "\\\\displaystyle", "\\displaystyle",
            "\\\\textstyle", "\\textstyle",
            "\\\\scriptstyle", "\\scriptstyle",
            "\\\\scriptscriptstyle", "\\scriptscriptstyle",
        ]
        
        for command in commandsToRemove {
            cleaned = cleaned.replacingOccurrences(of: command, with: "")
        }
        
        // Clean up braces that are no longer needed
        cleaned = cleanupUnnecessaryBraces(cleaned)
        
        // Clean up spacing
        cleaned = cleaned.replacingOccurrences(of: "  ", with: " ") // Double spaces
        cleaned = cleaned.replacingOccurrences(of: " ,", with: ",") // Space before comma
        cleaned = cleaned.replacingOccurrences(of: " .", with: ".") // Space before period
        
        // Remove backslashes that are no longer needed
        cleaned = cleaned.replacingOccurrences(of: "\\\\", with: "")
        cleaned = cleaned.replacingOccurrences(of: "\\", with: "")
        
        return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// Remove unnecessary braces after LaTeX conversion
    private func cleanupUnnecessaryBraces(_ text: String) -> String {
        var cleaned = text
        
        // Remove single-character braces like {x} -> x
        let singleCharPattern = #"\{([a-zA-Z0-9])\}"#
        do {
            let regex = try NSRegularExpression(pattern: singleCharPattern, options: [])
            cleaned = regex.stringByReplacingMatches(
                in: cleaned,
                options: [],
                range: NSRange(cleaned.startIndex..., in: cleaned),
                withTemplate: "$1"
            )
        } catch {
            print("Error cleaning up braces: \(error)")
        }
        
        // Remove empty braces
        cleaned = cleaned.replacingOccurrences(of: "{}", with: "")
        
        return cleaned
    }
    
    /// Enhanced function to convert LaTeX symbols to their Unicode equivalents for better display
    func enhanceLatexForDisplay(_ expression: String) -> String {
        // Use the comprehensive conversion system
        return convertLatexToUnicode(expression)
    }
    
    /// Escape LaTeX string for JavaScript/HTML rendering
    func escapeLaTeX(_ latex: String) -> String {
        return latex
            .replacingOccurrences(of: "\\", with: "\\\\")
            .replacingOccurrences(of: "\"", with: "\\\"")
            .replacingOccurrences(of: "\n", with: " ")
    }
    
    /// Debug function to test LaTeX validation - can be called from anywhere for testing
    func debugLatexValidation(_ text: String) -> (isValid: Bool, reason: String) {
        guard text.hasPrefix("$") && text.hasSuffix("$") && text.count > 2 else {
            return (false, "Missing $ delimiters or too short")
        }
        
        let innerText = String(text.dropFirst().dropLast())
        
        // Quick rejection tests (using updated limits)
        if innerText.count > 100 {
            return (false, "Too long (>100 chars): \(innerText.count)")
        }
        
        let spaceCount = innerText.filter({ $0 == " " }).count
        if spaceCount > 8 {
            return (false, "Too many spaces (\(spaceCount))")
        }
        
        // Currency pattern check
        if innerText.range(of: #"^\d+(\.\d+)?$"#, options: .regularExpression) != nil {
            return (false, "Looks like currency: \(innerText)")
        }
        
        // Common words check
        let separatorSet = CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters)
        let words = innerText.lowercased().components(separatedBy: separatorSet)
        let commonWords = words.filter { commonWordsSet.contains($0) }
        if !commonWords.isEmpty {
            return (false, "Contains common words: \(commonWords.joined(separator: ", "))")
        }
        
        // Positive indicators
        if containsMathCommands(innerText) {
            return (true, "Contains math commands")
        }
        if containsMathEquations(innerText) {
            return (true, "Contains math equations")
        }
        if containsMathSymbols(innerText) {
            return (true, "Contains math symbols")
        }
        if isShortMathExpression(innerText) {
            return (true, "Short math expression")
        }
        
        return (false, "No positive math indicators found for: '\(innerText)'")
    }
    
    /// Test simple number processing specifically for the $8$ and $12$ issue
    func testSimpleNumberProcessing(_ content: String) -> [String] {
        var results: [String] = []
        results.append("=== Testing Simple Number Processing ===")
        results.append("Input: '\(content)'")

        // Test if simple numbers like $8$ and $12$ are correctly identified as non-LaTeX
        let testNumbers = ["$8$", "$12$", "$5$", "$360$", "$90$"]

        for number in testNumbers {
            if content.contains(number) {
                let isValid = isValidLaTeX(number, type: .inlineMath)
                results.append("Number '\(number)' -> isValidLaTeX: \(isValid)")

                if isValid {
                    let debug = debugLatexValidation(number)
                    results.append("  Reason: \(debug.reason)")
                } else {
                    results.append("  ✅ Correctly identified as non-LaTeX (will be converted to plain text)")
                }
            }
        }

        return results
    }

    /// Test the complete processing pipeline for debugging
    func testLatexProcessing(_ content: String) -> [String] {
        var results: [String] = []
        results.append("=== Testing LaTeX Processing ===")
        results.append("Input: '\(content)'")
        
        // Test regex matching
        for (index, pattern) in latexPatterns.enumerated() {
            let range = NSRange(content.startIndex..<content.endIndex, in: content)
            let matches = pattern.matches(in: content, options: [], range: range)
            
            if !matches.isEmpty {
                results.append("Pattern \(index) matched \(matches.count) times:")
                for match in matches {
                    if let matchRange = Range(match.range, in: content) {
                        let matchedText = String(content[matchRange])
                        let type = LaTeXType.from(index: index)
                        let isValid = isValidLaTeX(matchedText, type: type)
                        results.append("  - '\(matchedText)' -> type: \(type), valid: \(isValid)")
                        
                        if !isValid && type == .inlineMath {
                            let debug = debugLatexValidation(matchedText)
                            results.append("    Reason: \(debug.reason)")
                        }
                    }
                }
            }
        }
        
        // Test final parsing
        let segments = parseContent(content)
        results.append("Final segments (\(segments.count)):")
        for segment in segments {
            results.append("  - '\(segment.text)' -> isLatex: \(segment.isLatex), type: \(segment.type)")
        }
        
        return results
    }
    
    /// Quick test function for debugging auto-delimiter detection
    func testAutoDelimiters(_ input: String) -> String {
        print("=== Testing Auto-Delimiter Detection ===")
        print("Input: '\(input)'")
        
        let processed = autoFixMissingDelimiters(input)
        print("After auto-fix: '\(processed)'")
        
        let segments = parseContent(processed)
        print("Segments (\(segments.count)):")
        for segment in segments {
            print("  - '\(segment.text)' -> isLatex: \(segment.isLatex), type: \(segment.type)")
        }
        
        return processed
    }
    
    // MARK: - Private Helper Methods
    
    private func isValidLaTeX(_ text: String, type: LaTeXType) -> Bool {
        switch type {
        case .displayMath, .blockMath:
            return text.count > 4 // Minimum meaningful length
            
        case .inlineMath:
            return isValidInlineMath(text)
            
        case .text:
            return false
        }
    }
    
    private func isValidInlineMath(_ text: String) -> Bool {
        guard text.hasPrefix("$") && text.hasSuffix("$") && text.count > 2 else {
            return false
        }
        
        let innerText = String(text.dropFirst().dropLast())
        
        // Quick rejection tests (made more lenient)
        if innerText.count > 100 { return false } // Increased from 50 to 100
        
        // Allow more spaces for complex expressions
        let spaceCount = innerText.filter({ $0 == " " }).count
        if spaceCount > 8 { return false } // Increased from 3 to 8
        
        // Currency pattern check - be more specific, but don't reject LaTeX escaped symbols
        // Only reject if it's a plain dollar amount without LaTeX commands
        if innerText.range(of: #"^\d+(\.\d+)?$"#, options: .regularExpression) != nil {
            return false  // Plain numbers like "8", "15", "15.00", "12.5", etc.
        }

        // Don't reject escaped dollar signs in LaTeX (e.g., \$15)
        // The containsMathCommands check below will catch these as valid LaTeX
        
        // Enhanced: Check for obvious LaTeX indicators first (before common word rejection)
        if containsMathCommands(innerText) { return true }
        if containsMathEquations(innerText) { return true }
        if containsMathSymbols(innerText) { return true }
        
        // Common words check - but allow if there are math symbols too
        let separatorSet = CharacterSet.whitespacesAndNewlines.union(.punctuationCharacters)
        let words = innerText.lowercased().components(separatedBy: separatorSet)
        let commonWords = words.filter { commonWordsSet.contains($0) }
        
        // If it has common words, only reject if it's MOSTLY common words and has no math indicators
        if !commonWords.isEmpty {
            // Be more lenient if it contains equals sign or obvious math patterns
            let hasEqualsSign = innerText.contains("=")
            let hasMathOperators = innerText.rangeOfCharacter(from: CharacterSet(charactersIn: "+-*/")) != nil
            
            if hasEqualsSign || hasMathOperators {
                // Allow up to 90% common words if it has math operators
                let commonWordRatio = Double(commonWords.count) / Double(words.count)
                if commonWordRatio > 0.9 {
                    return false
                }
            } else {
                // Original threshold for non-mathematical content
                let commonWordRatio = Double(commonWords.count) / Double(words.count)
                if commonWordRatio > 0.7 {
                    return false
                }
            }
        }
        
        // Additional positive indicators for mathematical expressions
        if hasGreekLetters(innerText) { return true }
        if hasSubscriptSuperscript(innerText) { return true }
        if hasMathematicalOperators(innerText) { return true }
        if hasGeometricSymbols(innerText) { return true }
        if isGeometricExpression(innerText) { return true }
        if isShortMathExpression(innerText) { return true }
        
        // Allow single variable expressions (letters only, not numbers)
        if innerText.trimmingCharacters(in: .whitespacesAndNewlines).count == 1 {
            let char = innerText.trimmingCharacters(in: .whitespacesAndNewlines).first!
            if char.isLetter {  // Only allow letters like $x$, $y$, $A$, not numbers like $8$
                return true
            }
        }
        
        return false
    }
    
    private func containsMathCommands(_ text: String) -> Bool {
        return mathCommandsSet.contains { command in
            text.contains(command)
        }
    }
    
    private func containsMathEquations(_ text: String) -> Bool {
        let patterns = [
            #"[a-zA-Z]\s*=\s*\d+"#,
            #"\d+\s*=\s*[a-zA-Z]"#,
            #"[a-zA-Z]\s*[+\-*/]\s*\d+"#,
            #"\d+\s*[+\-*/]\s*[a-zA-Z]"#,
            #"[a-zA-Z]\s*[+\-*/]\s*[a-zA-Z]"#,
            #"[a-zA-Z]\s*[+\-]\s*\d+\s*=\s*\d+"#,
            #"[a-zA-Z]\s*=\s*\d+\s*[+\-]\s*\d+"#,
            #"\d+\s*[+\-]\s*\d+\s*=\s*\d+"#
        ]
        
        return patterns.contains { pattern in
            text.range(of: pattern, options: .regularExpression) != nil
        }
    }
    
    private func containsMathSymbols(_ text: String) -> Bool {
        let patterns = [
            #"[a-zA-Z]\^[a-zA-Z0-9]"#,  // superscripts
            #"[a-zA-Z]_[a-zA-Z0-9]"#,   // subscripts
            #"\{[^}]+\}"#,               // braces
            #"[a-zA-Z]+/[a-zA-Z]+"#     // fractions
        ]
        
        return patterns.contains { pattern in
            text.range(of: pattern, options: .regularExpression) != nil
        }
    }
    
    private func isShortMathExpression(_ text: String) -> Bool {
        guard text.count <= 15 else { return false } // Increased from 10 to 15
        
        let mathCharacterSet = CharacterSet(charactersIn: "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789=+-*/^_(){}[]., ")
        let hasInvalidChars = text.rangeOfCharacter(from: mathCharacterSet.inverted) != nil
        let hasLetters = text.rangeOfCharacter(from: .letters) != nil
        let hasNumbers = text.rangeOfCharacter(from: .decimalDigits) != nil
        let hasEqualsSign = text.contains("=")
        let hasMathOperators = text.rangeOfCharacter(from: CharacterSet(charactersIn: "+-*/=")) != nil
        
        // If it has an equals sign or math operators with letters/numbers, it's likely math
        if hasEqualsSign && (hasLetters || hasNumbers) {
            return !hasInvalidChars
        }
        
        return !hasInvalidChars && hasLetters && hasNumbers && hasMathOperators
    }
    
    // Additional helper methods for better math detection
    private func hasGreekLetters(_ text: String) -> Bool {
        let greekLetters = ["alpha", "beta", "gamma", "delta", "epsilon", "zeta", "eta", "theta", 
                           "iota", "kappa", "lambda", "mu", "nu", "xi", "omicron", "pi", "rho", 
                           "sigma", "tau", "upsilon", "phi", "chi", "psi", "omega"]
        return greekLetters.contains { text.lowercased().contains($0) }
    }
    
    private func hasSubscriptSuperscript(_ text: String) -> Bool {
        return text.contains("_") || text.contains("^")
    }
    
    private func hasMathematicalOperators(_ text: String) -> Bool {
        let operators = ["±", "∓", "×", "÷", "∫", "∑", "∏", "√", "∂", "∇", "∞", "≤", "≥", "≠", "≈", "≡"]
        return operators.contains { text.contains($0) }
    }
    
    private func hasGeometricSymbols(_ text: String) -> Bool {
        let geometricSymbols = ["∠", "°", "△", "□", "◊", "∥", "⊥", "∢", "∡"]
        let geometricWords = ["angle", "triangle", "line", "intersect", "parallel", "perpendicular", "degree"]
        
        let hasSymbols = geometricSymbols.contains { text.contains($0) }
        let hasWords = geometricWords.contains { text.lowercased().contains($0) }
        
        return hasSymbols || hasWords
    }
    
    private func isGeometricExpression(_ text: String) -> Bool {
        // Check for angle notation like ∠ABC, ∠RVT
        let anglePattern = #"∠[A-Z]{2,3}"#
        if text.range(of: anglePattern, options: .regularExpression) != nil {
            return true
        }
        
        // Check for expressions with degree symbols
        if text.contains("°") {
            return true
        }
        
        // Check for common geometric terms combined with mathematical expressions
        let geometricTerms = ["lines", "intersect", "angle", "triangle", "measure"]
        let hasMathContent = text.rangeOfCharacter(from: CharacterSet(charactersIn: "=+-*/()")) != nil
        let hasGeometricTerms = geometricTerms.contains { text.lowercased().contains($0) }
        
        return hasGeometricTerms && hasMathContent
    }
    
    private func removeUnbalancedBraces(_ text: String) -> String {
        var result = text
        
        while result.hasSuffix("}") {
            let openBraces = result.filter { $0 == "{" }.count
            let closeBraces = result.filter { $0 == "}" }.count
            
            if closeBraces > openBraces {
                result = String(result.dropLast())
            } else {
                break
            }
        }
        
        return result
    }
    
    private func removeOverlappingMatches(_ matches: [(range: Range<String.Index>, text: String, type: LaTeXType)]) -> [(range: Range<String.Index>, text: String, type: LaTeXType)] {
        var result: [(range: Range<String.Index>, text: String, type: LaTeXType)] = []
        
        for match in matches {
            let overlaps = result.contains { existing in
                match.range.overlaps(existing.range)
            }
            
            if !overlaps {
                result.append(match)
            }
        }
        
        return result
    }
    
    private func buildSegments(from text: String, matches: [(range: Range<String.Index>, text: String, type: LaTeXType)]) -> [ContentSegment] {
        var segments: [ContentSegment] = []
        var lastEnd = text.startIndex
        
        for match in matches {
            // Add text before this LaTeX match
            if lastEnd < match.range.lowerBound {
                let textBefore = String(text[lastEnd..<match.range.lowerBound])
                if !textBefore.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    segments.append(ContentSegment(text: textBefore, isLatex: false, type: .text))
                }
            }
            
            // Add the LaTeX match
            segments.append(ContentSegment(text: match.text, isLatex: true, type: match.type))
            lastEnd = match.range.upperBound
        }
        
        // Add remaining text
        if lastEnd < text.endIndex {
            let remainingText = String(text[lastEnd...])
            if !remainingText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                segments.append(ContentSegment(text: remainingText, isLatex: false, type: .text))
            }
        }
        
        // If no matches found, treat entire content as text
        if segments.isEmpty {
            return [ContentSegment(text: text, isLatex: false, type: .text)]
        }
        
        return segments
    }
}

// MARK: - Supporting Types

enum LaTeXType {
    case displayMath    // $$...$$
    case blockMath      // \[...\]
    case inlineMath     // $...$, \(...\)
    case text
    
    static func from(index: Int) -> LaTeXType {
        switch index {
        case 0: return .displayMath
        case 1, 2: return .blockMath
        case 3: return .inlineMath
        default: return .text
        }
    }
    
    var isDisplayMode: Bool {
        switch self {
        case .displayMath, .blockMath: return true
        case .inlineMath, .text: return false
        }
    }
}

// Enhanced content segment with type information
struct ContentSegment: Identifiable {
    let id = UUID()
    let text: String
    let isLatex: Bool
    let type: LaTeXType
    
    init(text: String, isLatex: Bool, type: LaTeXType = .text) {
        self.text = text
        self.isLatex = isLatex
        self.type = type
    }
} 

// MARK: - String Extension for Easy LaTeX-to-Unicode Conversion

extension String {
    /// Convert LaTeX expressions in this string to Unicode symbols
    /// - Parameter preserveDelimiters: Whether to keep original LaTeX delimiters (default: false)
    /// - Returns: String with LaTeX converted to Unicode symbols
    func convertingLatexToUnicode(preserveDelimiters: Bool = false) -> String {
        if preserveDelimiters {
            // Parse content and convert only LaTeX segments, preserving structure
            let segments = LaTeXProcessor.shared.parseContent(self)
            return segments.map { segment in
                if segment.isLatex {
                    let converted = LaTeXProcessor.shared.convertLatexToUnicode(segment.text)
                    // Re-wrap in appropriate delimiters if requested
                    switch segment.type {
                    case .displayMath:
                        return "$$\(converted)$$"
                    case .blockMath:
                        return "\\[\(converted)\\]"
                    case .inlineMath:
                        return "$\(converted)$"
                    case .text:
                        return converted
                    }
                } else {
                    return segment.text
                }
            }.joined()
        } else {
            // Convert entire string, removing all LaTeX delimiters
            return LaTeXProcessor.shared.convertLatexToUnicode(self)
        }
    }
    
    /// Quick check if this string contains LaTeX expressions
    var containsLaTeX: Bool {
        let patterns = [
            #"\$\$[^$]+?\$\$"#,          // Display math: $$ ... $$
            #"\\?\[[^\]]+?\\?\]"#,       // Display math: \[ ... \]
            #"\\?\([^)]+?\\?\)"#,        // Inline math: \( ... \)
            #"\$[^$\n]{1,50}\$"#         // Inline math: $ ... $
        ]
        
        return patterns.contains { pattern in
            self.range(of: pattern, options: .regularExpression) != nil
        }
    }
    
    /// Get a preview of how this string would look with LaTeX converted to Unicode (truncated)
    var latexToUnicodePreview: String {
        let converted = self.convertingLatexToUnicode()
        return converted.count > 100 ? String(converted.prefix(97)) + "..." : converted
    }
    
    /// Convert specific LaTeX symbols to Unicode (useful for partial conversion)
    func convertingSpecificLatexSymbols() -> String {
        var result = self
        
        // Quick conversions for common symbols (without full parsing)
        let quickConversions = [
            ("\\alpha", "α"), ("\\beta", "β"), ("\\gamma", "γ"), ("\\delta", "δ"),
            ("\\theta", "θ"), ("\\lambda", "λ"), ("\\mu", "μ"), ("\\pi", "π"),
            ("\\sigma", "σ"), ("\\phi", "φ"), ("\\omega", "ω"),
            ("\\times", "×"), ("\\div", "÷"), ("\\pm", "±"),
            ("\\leq", "≤"), ("\\geq", "≥"), ("\\neq", "≠"), ("\\approx", "≈"),
            ("\\infty", "∞"), ("\\partial", "∂"), ("\\nabla", "∇"),
            ("\\circ", "°"), ("\\deg", "°")
        ]
        
        for (latex, unicode) in quickConversions {
            result = result.replacingOccurrences(of: latex, with: unicode)
        }
        
        return result
    }
} 